"use client"
import LocationIcon from "@/components/common/svg/LocationIcon"
import CustomButton from "@/components/UI/CustomButton"
import { CirclePlus, SquarePen } from "lucide-react"
import { useState, useEffect } from "react";
import UserAddressItem from "./UserAddressItem";
import Map from "@/public/assets/images/map.png"
import dynamic from "next/dynamic";
import { UserAddress } from "@/lib/types/types";
const AddressModal = dynamic(() => import('./AddressModal'), {
    // loading: () => <p>The map is loading</p>,
    ssr: false,
});
const UserAddresses = ({addressList, selectedAddress, setSelectedAddress}: {addressList: UserAddress[], selectedAddress: string, setSelectedAddress: (id: string) => void}) => {
    // const [selected, setSelected] = useState<string>(addressList[0]?.id || "");
    const [isModalOpen, setIsModalOpen] = useState(false)

    // Debug: Log when selected state changes
    useEffect(() => {
        console.log('Selected address changed to:', selectedAddress);
    }, [selectedAddress]);

    // Function to open the modal
    const openModal = () => setIsModalOpen(true)

    // Function to close the modal
    const closeModal = () => setIsModalOpen(false)

    return (
        <div className='max-md:w-full bg-white rounded-3xl md:p-8 max-md:p-5 md:min-h-[28rem] max-md:min-h-80 '>
            <div className="header flex justify-between">
                <div className='flex flex-col gap-3 relative pb-5 max-md:pb-3 title-bt-border'>
                    <h2 className='text-xl font-black max-md:text-base'>آدرس ارسال کالا ها</h2>
                </div>
                <div>
                    <LocationIcon className="max-md:w-10 max-md:h-10" size="60" />
                </div>
            </div>
            <div className="mt-5 md:w-[50%] flex md:gap-5 max-md:gap-2 ">
                <CustomButton onClick={openModal}  className="py-4 px-0 max-md:px-2 max-md:py-2 max-md:text-xs">
                <CirclePlus size={22} className="max-md:w-5" />  افزودن آدرس جدید
                </CustomButton>
                <CustomButton className="bg-[#F9FAFB] text-gray-500 max-md:px-0 border max-md:text-xs">
                <SquarePen size={22} className="max-md:w-5"  />  مدیریت آدرس ها
                </CustomButton>
            </div>
            <AddressModal open={isModalOpen} onClose={closeModal} />

            <div className="mt-5">
                {addressList.map((item) => (
                    <UserAddressItem
                        key={item.id}
                        id={item.id}
                        selected={selectedAddress}
                        onSelect={(id) => setSelectedAddress(id)}
                        image={Map}
                        selectable={true}
                        address={item.address}
                        province={item.province}
                        city={item.city}
                        receiver_name={item.receiver_name}
                    />
                ))}
            </div>

        </div>
    )
}

export default UserAddresses