import CheckoutProgress from "@/components/shop/checkout/CheckoutProgress"
import ShippingPageClient from "@/components/shop/checkout/ShippingPageClient"
import { UserAddressesResponse } from "@/lib/types/types"
import { getUserAddresses } from "@/lib/utils"

const CheckoutShippingPage = async () => {
  const userAddresses:UserAddressesResponse = await getUserAddresses()
  console.log(userAddresses);

  return (
    <main className='container mx-auto  mb-16'>
       <CheckoutProgress
                steps={[
                  { title: 'سبد خرید', status: 'completed' },
                  { title: 'انتخاب آدرس', status: 'current' },
                  { title: 'پرداخت', status: 'upcoming' },
                ]}
              />

       <ShippingPageClient userAddresses={userAddresses} />

    </main>
  )
}

export default CheckoutShippingPage