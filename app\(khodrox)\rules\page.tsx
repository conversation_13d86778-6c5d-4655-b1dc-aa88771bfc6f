import Card from '@/components/common/Card'
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
// import RulesAndConditions from '@/components/InquiryStaticComponents/RulesAndConditions'
import { PageContentResponse } from '@/lib/types/types';
import { getPageContent } from '@/lib/utils';

export async function generateMetadata() {
    const data = await getPageContent("rules");

    return {
        title: data.meta_title,
        description: data.meta_description,
        robots: 'noindex, nofollow'
    };
}

const RulesPage = async () => {
    const data: PageContentResponse = await getPageContent("rules")

    const { description, title } = data
    return (
        <section className='max-w-7xl mx-auto mb-10 max-md:p-3'>
            <div className='mt-10'>
                <h1 className='text-center text-3xl max-md:text-xl font-bold mb-8 text-gray-800'>
                    {title || ""}
                </h1>
            </div>
            <Card className='max-w-5xl mx-auto mt-8'>
                {/* <RulesAndConditions /> */}
                {
                    description &&
                    <ArticleSection description={description} />
                }
            </Card>
        </section>
    )
}

export default RulesPage