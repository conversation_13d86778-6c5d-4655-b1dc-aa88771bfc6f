'use client'

import {useRouter, useSearchParams} from "next/navigation";
import toast from "react-hot-toast";
import DialogModal from "@/components/common/DialogModal";
import CustomButton from "@/components/UI/CustomButton";
import {ViolationQueryParams} from "@/lib/types/types";
import useInquiryPost from "@/lib/hooks/useInquiryPost";
import {RE_INQUIRY} from "@/lib/constants";
import {CarPaymentDetails} from "@/lib/types/action-types";

type Props = {
    data: CarPaymentDetails
}

export default function ReInquire({data}: Props) {
    const {mutate, isLoading, setIsLoading} = useInquiryPost();
    const reInquiry = useSearchParams().get(RE_INQUIRY);
    const router = useRouter()
    const modalDescription = " در صورت ادامه هزینه استعلام جدید از حساب شما کسر میگردد."
    const modalTitle = "آیا میخواهید استعلام جدید بگیرید ؟"

    async function handleReInquiry() {
        const queryParams: ViolationQueryParams = {
            reInquiry: 'true',
            withDetails: data.details ? 'true' : 'false',
            phoneNumber: data?.plaque_details?.phone,
            nationalCode: data?.plaque_details?.national_id,
            left: data.plaque.left,
            right: data.plaque?.right,
            middle: data.plaque?.mid,
            inquiry: 'true',
            alphabet: data.plaque?.alphabet,
            isMotor: data.type === 'motor' ? 'true' : 'false',
        }
        const mutateResult = await mutate(queryParams);
        if (!mutateResult.success && mutateResult.href) {
            router.push(mutateResult.href)
        } else if (!mutateResult.success && mutateResult.message) {
            toast.error(mutateResult.message);
        } else if (mutateResult.success && mutateResult.href) {
            router.replace(mutateResult.href)
        }
    }

    async function onReInquireConfirmed() {
        await handleReInquiry()
    }

    return (
        <div className='w-full flex justify-between items-center'>
            {reInquiry && reInquiry === 'true' &&
                <DialogModal title={modalTitle} onConfirm={onReInquireConfirmed} description={modalDescription}>
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variant='green'
                        className='mt-2 py-4'>استعلام
                        جدید</CustomButton>
                </DialogModal>
            }
        </div>
    );
}
