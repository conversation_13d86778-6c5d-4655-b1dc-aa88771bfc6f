import { ChevronLeft } from "lucide-react"
import Image from "next/image"
import LearnMorePic from "@/public/assets/images/learn-more.png";

const LearnMore = () => {
  return (
    <section className="bg-yellow p-5 max-w-[950px] max-md:w-[96%] mx-auto max-md:p-3 rounded-3xl flex max-md:flex-col items-center gap-5">
      <div className="max-md:w-full">
        <Image src={LearnMorePic} className="max-md:w-full" alt="learn-more" />
      </div>
      <div className="flex flex-col justify-between gap-3">
        <h3 className="md:text-lg w-fit pb-1.5 text-bold text-black relative learn-more-title">
          عنوان نوشته بیشتر بدانید بلاگ شما در اینجا قرار میگرد
        </h3>
        <p className="text-sm text-black max-md:text-xs max-md:leading-6">
          لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.
        </p>
        <div className="max-md:w-full">
          <button className="bg-[#DEAB10] text-white max-md:w-full max-md:text-sm flex items-center max-md:justify-between max-md:gap-1 p-2 rounded-full">
            مطالعه کامل
            <ChevronLeft size={20} />
          </button>

        </div>
      </div>

    </section>
  )
}

export default LearnMore