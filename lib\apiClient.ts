"use server"

import { cookies } from "next/headers";

interface ApiClientOptions {
    method?: "GET" | "POST" | "PUT" | "DELETE";
    headers?: Record<string, string>;
    body?: any;
    credentials?: RequestCredentials;
    next?: {
      tags?: string[];
      revalidate?: number; // <-- Add this line
    };
  }
  // utils/apiClient.ts
  
  
  const BASE_URL = process.env.BASE_URL as string;
  const APPLICATION_TOKEN = process.env.X_APPLICATION_TOKEN as string;
  
  
  export async function apiClient(endpoint: string, options: ApiClientOptions = {}) {
    const { method, headers = {}, body, credentials = "include", next } = options;
  
    
    const defaultHeaders = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "X-Application-Token": APPLICATION_TOKEN,
    };
    
    
  
    
    const finalHeaders: Record<string, string> = { ...defaultHeaders, ...headers };
  
   
    const cookieStore = await cookies();
    const accessToken = cookieStore.get("access_token")?.value;
    if (accessToken) {
      finalHeaders.Authorization = `${accessToken}`;
    }
    // console.log(finalHeaders);
    
  
    try {
      return await fetch(`${BASE_URL}${endpoint}`, {
        method,
        headers: finalHeaders,
        credentials,
        body: body ? JSON.stringify(body) : undefined,
        next,
      });
  
      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.message || "خطایی رخ داده است");
      // }
  
      // return await response.json();
    } catch (error) {
      console.error("API call error:", error);
      throw error;
    }
  }