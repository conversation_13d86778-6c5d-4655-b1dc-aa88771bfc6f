import { Detail } from "@/lib/types/product.types"
import {FC} from "react"

const ProductInfoItem: FC<{ detail: Detail }> = ({ detail }) => {
    return (
        <div className='flex max-md:justify-between md:gap-16 my-7 text-sm'>
            <div className='md:w-[30%] md:text-left py-3'>
                {/* <Image /> */}
                <p className='pluses'> {detail.key} </p>
            </div>
            <div className='bg-gray-100 md:w-full max-md:w-[60%] rounded-2xl'>
                <p className='py-3 px-2 max-md:px-3'>
                    {detail.value}
                </p>
            </div>
        </div>
    )
}

export default ProductInfoItem