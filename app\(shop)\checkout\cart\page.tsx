import CheckoutProgress from '@/components/shop/checkout/CheckoutProgress'
import BasketCart from '@/components/shop/checkout/BasketCart'
import DiscountCode from '@/components/shop/checkout/DiscountCode'
import FactorCard from '@/components/shop/checkout/FactorCard'
import PaymentMethodCard from '@/components/shop/checkout/PaymentMethodCard'
import OrderSummary from '@/components/shop/checkout/OrderSummary'
import UserAddresses from '@/components/shop/checkout/UserAddresses'
import { getUserCart } from '@/actions/cart.action'



const CheckoutCartPage = async () => {
  
  
  return (
    <>
      <main className='container mx-auto  mb-16'>

        <CheckoutProgress
          steps={[
            { title: 'سبد خرید', status: 'current' },
            { title: 'انتخاب آدرس', status: 'upcoming' },
            { title: 'پرداخت', status: 'upcoming' },
          ]}
        />
        <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>

          <div className='md:w-[70%] '>
            <BasketCart />
            {/* <PaymentMethodCard /> */}
            {/* <UserAddresses /> */}

            <DiscountCode />
            {/* <OrderSummary /> */}
          </div>

          <FactorCard steps={{title: "cart", nextStepBtnTitle: "تایید و تکمیل سفارش", nextStepBtnLink: "/checkout/shipping"}} />
        </div>


      </main>
    </>

  )
}

export default CheckoutCartPage