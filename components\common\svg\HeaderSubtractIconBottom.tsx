import Image from "next/image";
import {cn} from "@/lib/utils";

type Props = {
    width?: number
    height?: number
    className?: string
}

export default function HeaderSubtractIconBottomIcon({width, height, className}: Props) {
    return (
        <Image src='/assets/images/header-subtract-bottom.svg' width={width} height={height} alt='car inquiry'
               className={cn(className)}/>
    );
}
