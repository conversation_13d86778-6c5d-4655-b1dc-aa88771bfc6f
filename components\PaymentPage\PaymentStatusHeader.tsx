import { Check, X } from "lucide-react";

/**
 * Component for the payment status header
 * Displays success or failure status with appropriate styling
 */
const PaymentStatusHeader: React.FC<{
  isSuccess: boolean;
}> = ({ isSuccess }) => (
  <div className={`payment-card-header bg-gradient-to-b ${isSuccess ? "from-green-200" : "from-red-200"} text-black flex justify-start p-5 items-center gap-5 h-32 rounded-2xl`}>
    <div className={`${isSuccess ? "bg-green-500" : "bg-red-700"} rounded-full p-1`}>
      {isSuccess ? (
        <Check size={35} className='stroke-white' />
      ) : (
        <X size={35} className='stroke-white' />
      )}
    </div>
    <div className='text-right'>
      <h2 className={`text-xl font-bold ${isSuccess ? "text-green-500" : "text-red-700"}`}>
        {isSuccess ? "وضعیت پرداخت موفق" : "وضعیت پرداخت ناموفق"}
      </h2>
      {/* {statusDescription && <p className="text-sm mt-1 text-gray-600">{statusDescription}</p>} */}
    </div>
  </div>
);

export default PaymentStatusHeader