import { ChevronLeft } from "lucide-react"

const BlogCategoriesList = () => {
    return (
        <section className="px-1">
            <h3 className="sidebar-title relative px-5 mb-5 mt-8">
                دسته بندی مطالب
            </h3>
            {/* TODO: this part of code must become dynamic to use in shop main page */}
            <ul className='flex flex-col gap-6 mb-3 text-sm mt-5'>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما  (11)
                    <ChevronLeft className='text-gray-400' />
                </li>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما  (131)
                    <ChevronLeft className='text-gray-400' />
                </li>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما  (311)
                    <ChevronLeft className='text-gray-400' />
                </li>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما  (141)
                    <ChevronLeft className='text-gray-400' />
                </li>

            </ul>

        </section>
    )
}

export default BlogCategoriesList