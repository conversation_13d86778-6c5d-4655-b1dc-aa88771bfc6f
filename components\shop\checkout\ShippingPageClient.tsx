"use client"
import UserAddresses from './UserAddresses'
import FactorCard from './FactorCard'
import { UserAddressesResponse } from '@/lib/types/types'
import { useState } from 'react'

const ShippingPageClient = ({ userAddresses }: { userAddresses: UserAddressesResponse }) => {
    const [selectedAddress, setSelectedAddress] = useState<string>("");
    return (
        <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>

            <div className='md:w-[70%]'>
                <UserAddresses
                    selectedAddress={selectedAddress}
                    setSelectedAddress={setSelectedAddress}
                    addressList={userAddresses.data} />
            </div>
            <FactorCard
            selectedAddress={selectedAddress}
                steps={{title: "shipping", nextStepBtnTitle: "ثبت آدرس", nextStepBtnLink: `/checkout/payment?address=${selectedAddress}`}}
            />
        </div>
    )
}

export default ShippingPageClient