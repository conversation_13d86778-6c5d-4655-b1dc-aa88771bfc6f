import Card from '@/components/common/Card'
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import { PageContentResponse } from '@/lib/types/types';
import { getPageContent } from '@/lib/utils';

export async function generateMetadata() {
    const data = await getPageContent("privacy-policy");

    return {
        title: data.meta_title,
        description: data.meta_description,
        robots: 'noindex, nofollow'
    };
}


const PrivacyPolicyPage = async () => {
  const data: PageContentResponse = await getPageContent("privacy-policy")
  const { description, title } = data
      
  return (
    <section className='max-w-7xl mx-auto mb-10 max-md:p-3'>
        <div className='mt-10'>
            <h1 className='text-center text-3xl max-md:text-xl font-bold mb-8 text-gray-800'>
              {title || ""}
            </h1>
        </div>
        <Card className='max-w-5xl mx-auto mt-8'>
            {/* <PrivacyPolicy /> */}
            {
              description &&
            <ArticleSection description={description} />
            }
        </Card>
    </section>
  )
}

export default PrivacyPolicyPage