import Container from '@/components/common/Container';
import { PageDescription } from '@/components/common/PageDescription';
import '@/styles/styles.css';
import InquiryFormClientWrapper from '@/components/inquiry/InquiryFormClientWrapper';
import { ServiceStatusType } from '@/lib/types/types';

type Props = {
  isMotor: boolean;
  withDetails?: boolean;
  title: string;
  status?: ServiceStatusType;
};

export default async function InquiryComponent({ isMotor, withDetails, title, status = 'ACTIVE' }: Props) {
  return (
    <Container className='mb-16'>
      <div className='w-full max-w-[553px]'>
        <PageDescription
          title={title}
          description='پلاک خود را وارد کرده تا از وضعیت پرداختی‌های خود مطلع شوید.'
        />
        <InquiryFormClientWrapper isMotor={isMotor} withDetails={withDetails} status={status} />
      </div>
    </Container>
  );
}
