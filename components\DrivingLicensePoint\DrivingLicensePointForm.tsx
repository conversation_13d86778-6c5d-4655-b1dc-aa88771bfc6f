'use client'

import {useForm} from "react-hook-form";
import {
    NATIONAL_CODE_MAX_LENGTH, PHONE_NUMBER_MAX_LENGTH
} from "@/lib/constants";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/UI/form";
import Card from "@/components/common/Card";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import CustomInput from "@/components/UI/CustomInput";
import IdCardIcon from "@/components/common/svg/IdCardIcon";
import MobileInputIcon from "@/components/common/svg/MobileInputIcon";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CustomButton from "@/components/UI/CustomButton";
import {Captions, IdCard, X} from "lucide-react";
import {DrivingLicensePointFormSchema, DrivingLicensePointType} from "@/lib/types/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import StatusMessage from "@/components/common/StatusMessage";
import CircleMinusRedIcon from "@/components/common/svg/CircleMinusRedIcon";
import {ServiceStatusType} from "@/lib/types/types";
import { usePathname } from "next/navigation";
import CertificateStatusIcon from "../common/svg/services/CertificateStatusIcon";
type Props = {
    status?: ServiceStatusType;
}

export function DrivingLicensePointForm({status}: Props) {
    const path = usePathname()
    console.log(path);
    

    const form = useForm<DrivingLicensePointType>({
        mode: 'onSubmit',
        resolver: zodResolver(DrivingLicensePointFormSchema),
        defaultValues: {
            drivingLicense: "",
            nationalCode: "",
            phoneNumber: "",
        },
    })


    async function onSubmit(values: DrivingLicensePointType) {
        if (status === 'COMING_SOON' || status === "DISABLED") return;
    }

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="mt-5 md:mt-10"
                autoComplete="off"
            >
                <Card className='!px-0 !pt-5 !pb-10  mt-5'>
                    {
                        path == "/driving-license-point" ?
                        <InquiryHeader
                            icon={<div className='bg-transparent w-[50px] h-[50px] flex justify-center items-center'>
                                <CircleMinusRedIcon height={35} width={35}/></div>}
                            title="استعلام نمره منفی"
                        />
                        :
                        <InquiryHeader
                        icon={<div className='bg-transparent w-[50px] h-[50px] flex justify-center items-center'>
                                <IdCard color="#7495D1" height={35} width={35}/></div>}
                            title="استعلام وضعیت گواهینامه"
                        />
                    }
                    <div className='px-5 mt-2 md:px-14'>
                        <div className='mt-2'>
                            <div>
                                <FormField
                                    control={form.control}
                                    name="nationalCode"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className='text-[#596068] text-xs'>کد ملی</FormLabel>
                                            <FormControl>
                                                <CustomInput
                                                    variant='secondary'
                                                    allowOnlyNumbers
                                                    maxLength={NATIONAL_CODE_MAX_LENGTH}
                                                    placeholder='کد ملی را وارد کنید'
                                                    {...field}
                                                    leftIcon={<IdCardIcon height={20} width={20}/>}
                                                    direction='rtl'
                                                    inputMode='numeric'
                                                    autoFocus
                                                />
                                            </FormControl>
                                            <FormMessage className='text-xs'/>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className='mt-4'>
                                <FormField
                                    control={form.control}
                                    name='drivingLicense'
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className='text-[#596068] text-xs'>گواهینامه رانندگی</FormLabel>
                                            <FormControl>
                                                <CustomInput
                                                    variant='secondary'
                                                    allowOnlyNumbers
                                                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                                                    leftIcon={<Captions height={22} width={22}
                                                                        className='text-gray-400'/>}
                                                    placeholder='شماره گواهینامه رانندگی را وارد کنید'
                                                    {...field}
                                                    inputMode='numeric'
                                                    direction='rtl'
                                                />
                                            </FormControl>
                                            <FormMessage className='text-xs'/>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className='mt-4'>
                                <FormField
                                    control={form.control}
                                    name="phoneNumber"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className='text-[#596068] text-xs'>شماره موبایل</FormLabel>
                                            <FormControl>
                                                <CustomInput
                                                    variant='secondary'
                                                    allowOnlyNumbers
                                                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                                                    leftIcon={<MobileInputIcon width={15} height={15}/>}
                                                    placeholder='شماره موبایل را وارد کنید'
                                                    {...field}
                                                    inputMode='numeric'
                                                    direction='rtl'
                                                />
                                            </FormControl>
                                            <FormMessage className='text-xs'/>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className='mt-5'>
                                <ChoiceWrapper
                                    backgroundColor='#FFF5D8'
                                    borderColor='#F7BC06'
                                >
                                    <div className='flex py-3 flex-col gap-y-1'>
                                        <p className='text-[#5E646B] text-xs md:text-sm'>هزینه استعلام: 16,170 تومان</p>
                                        <p className='text-[#5E646B] text-xs md:text-sm'>خودرو هاب هیچ دخل و تصرفی در
                                            تعیین
                                            این هزینه
                                            ندارد</p>
                                    </div>
                                </ChoiceWrapper>
                            </div>
                            <CustomButton
                                // loading={isLoading}
                                disabled={(status === 'COMING_SOON' || status === "DISABLED")}
                                type='submit'
                                className='mt-5 !py-5'
                            >
                                استعلام
                            </CustomButton>
                            {(status === 'COMING_SOON' || status === "DISABLED") && <div className='mt-5'>
                                <StatusMessage status={status}/>
                            </div>}
                        </div>
                    </div>

                </Card>
            </form>
        </Form>
    );
}