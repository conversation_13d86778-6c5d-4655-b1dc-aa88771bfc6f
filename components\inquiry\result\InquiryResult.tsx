import Card from "@/components/common/Card";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import InquiryResultWithDetails from "@/components/inquiry/result/InquiryResultWithDetails";
import CustomButton from "@/components/UI/CustomButton";
import TickMarkCircleIcon from "@/components/common/svg/TickMarkCircleIcon";
import BackLink from "@/components/common/BackLink";
import {CarPaymentDetails} from "@/lib/types/action-types";
import DisplayPlaque from "@/components/common/DisplayPlaque";
import InquiryResultNoDetails from "@/components/inquiry/result/inquiryResultNoDetails";
import ReInquire from "@/components/inquiry/result/ReInquire";

type Props = {
    inquiryDetail: CarPaymentDetails
    isReInquiry: boolean
}

export default function InquiryResult({inquiryDetail, isReInquiry}: Props) {
    const isMotor = inquiryDetail.type === "motor";
    const {mid, left, right, alphabet} = inquiryDetail.plaque
    return (
        <Card className="relative !pt-0 py-7 z-20">
            <div
                className='absolute top-0 right-[20px] md:right-[50px] w-[38px] h-[65px] bg-[#F9FAFB] rounded-b-full'></div>
            <div className='absolute top-[30px] right-[25px] md:right-[54px]'>
                <TickMarkCircleIcon/>
            </div>
            <BackLink/>
            <div className="mt-10 w-full justify-center items-center flex flex-col gap-y-7">
                <h2 className='text-[#212121] text-sm font-semibold'>نتیجه استعلام وسیله نقلیه شما</h2>
                <DisplayPlaque left={left || ""} right={right || ""} middle={mid || ""} alphabet={alphabet || ""}
                               isMotor={isMotor}/>
                {
                    inquiryDetail.with_detail_type && (
                        (inquiryDetail?.details ?? []).map((item, index) => (
                            <InquiryResultWithDetails key={index} detail={item}/>
                        ))
                    )
                }
                {
                    !inquiryDetail.with_detail_type && (
                        <InquiryResultNoDetails inquiryDetails={inquiryDetail}/>
                    )
                }

                <ChoiceWrapper
                    backgroundColor='#FFF5D8'
                    borderColor='#F7BC06'
                >
                    <div className='w-full flex flex-col gap-y-2 py-3 px-2'>
                        {isReInquiry && <div className='w-full'>
                            <p className='text-sm font-semibold text-justify text-destructive'>توجه: استعلام وسیله نقلیه
                                شما
                                قدیمی
                                است و
                                ممکن است
                                اطلاعات آن
                                به ‌روز نباشد. برای مشاهده استعلام
                                جدید، لطفاً دکمه &quot;استعلام جدید&quot; را انتخاب کنید.</p>
                        </div>}
                        <div className='w-full text-xs flex mt-2 justify-between items-center'>
                            <span>تاریخ استعلام:</span>
                            <span className=''>{inquiryDetail.date_inquiry}</span>
                        </div>
                        <div className='w-full flex justify-between items-center'>
                            <span>جمع کل:</span>
                            <span className='text-[#000000]'>{inquiryDetail.amount}<span
                                className='text-xs mx-1'>تومان</span></span>
                        </div>
                    </div>
                </ChoiceWrapper>
            </div>
            <div className="mt-3">
                {
                    inquiryDetail.payment_url ?
                    <>
                        
                    <CustomButton className='!py-4' target='_blank' href={inquiryDetail.payment_url}>پرداخت
                        کلی</CustomButton>
                    </>
                :
                <p className="text-red-500 text-base text-center py-3">
                    {inquiryDetail.message}
                </p>
                }
                 {isReInquiry && <ReInquire data={inquiryDetail}/>}
            </div>
        </Card>
    );
}
