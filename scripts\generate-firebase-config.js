/**
 * This script generates the Firebase configuration file for the service worker
 * It reads the configuration from environment variables and writes it to a file
 */

// Load environment variables from .env file
require('dotenv').config();

const fs = require('fs');
const path = require('path');

// Fallback values in case environment variables are not set
const fallbackConfig = {
  apiKey: "AIzaSyCRAAOAiMfhgZylH2zHKIklwObtLN2iNco",
  authDomain: "khodrox-fb6ad.firebaseapp.com",
  projectId: "khodrox-fb6ad",
  storageBucket: "khodrox-fb6ad.firebasestorage.app",
  messagingSenderId: "442127983166",
  appId: "1:442127983166:web:1798e640b22d0f160de76f",
  measurementId: "G-CM6BYT6KHY"
};

// Get the Firebase configuration from environment variables with fallbacks
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || fallbackConfig.apiKey,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || fallbackConfig.authDomain,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || fallbackConfig.projectId,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || fallbackConfig.storageBucket,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || fallbackConfig.messagingSenderId,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || fallbackConfig.appId,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || fallbackConfig.measurementId
};

// Create the content of the configuration file
const fileContent = `// This file is generated by the generate-firebase-config.js script
// It contains the Firebase configuration for the service worker
const firebaseConfig = ${JSON.stringify(firebaseConfig, null, 2)};`;

// Write the configuration to a file
const filePath = path.join(__dirname, '../public/firebase-config.js');
fs.writeFileSync(filePath, fileContent);

console.log('Firebase configuration file generated successfully!');
