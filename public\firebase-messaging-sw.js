// Use importScripts for service workers
// These are the compat versions which work in service workers
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-messaging-compat.js');

firebase.initializeApp({
    apiKey: "AIzaSyCRAAOAiMfhgZylH2zHKIklwObtLN2iNco",
    authDomain: "khodrox-fb6ad.firebaseapp.com",
    projectId: "khodrox-fb6ad",
    storageBucket: "khodrox-fb6ad.firebasestorage.app",
    messagingSenderId: "442127983166",
    appId: "1:442127983166:web:1798e640b22d0f160de76f",
    measurementId: "G-CM6BYT6KHY"
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage(function (payload) {
    console.log('[firebase-messaging-sw.js] Background message:', payload);
  
    // Skip manual display if notification payload exists (Firebase will auto-show it)
    if (payload.notification) {
      return;
    }
  
    // For data-only messages, manually show notification
    const notificationTitle = payload.data?.title || 'Default Title';
    const notificationOptions = {
      body: payload.data?.body || 'Default body',
      icon: '/firebase-logo.png',
    };
  
    self.registration.showNotification(notificationTitle, notificationOptions);
  });
  