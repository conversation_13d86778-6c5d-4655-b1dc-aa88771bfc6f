export interface InvoiceProductDetail {
  key: string;
  value: string;
}

export interface InvoiceProduct {
  id: string;
  variant_id: string;
  name: string;
  price: number;
  sale_price: number;
  discount: number;
  quantity: number;
  total: number;
  image: string | null;
  details: InvoiceProductDetail[];
}

export interface InvoiceAddress {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: number;
  latitude: number;
  longitude: number;
}

export interface InvoiceData {
  id: string;
  status: string;
  creation_date: string;
  products: InvoiceProduct[];
  total: number;
  total_discount: number;
  address: InvoiceAddress;
}

// Main response type
export interface CreateInvoiceResponse {
  success: boolean;
  message: string;
  status: number;
  data: InvoiceData | {
    cart: string[];
  };
}
