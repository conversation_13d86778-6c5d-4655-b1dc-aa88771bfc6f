export async function registerFirebaseSw(swPath = '/firebase-messaging-sw.js') {
    if (!('serviceWorker' in navigator)) {
        console.warn('Service Workers are not supported');
        return null;
    }

    try {
        const registration = await navigator.serviceWorker.register(swPath);
        console.log('✅ Service Worker registered:', registration);
        return registration;
    } catch (err) {
        console.error('❌ Failed to register SW:', err);
        return null;
    }
}