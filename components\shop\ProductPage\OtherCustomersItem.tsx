import SpecialPrice from "@/components/common/SpecialPrice"
import CustomButton from "@/components/UI/CustomButton"
import { Award, BadgeCheck, BaggageClaim, Store } from "lucide-react"
const OtherCustomersItem = () => {
    return (
        <div className="flex max-md:flex-col gap-3 bg-white p-5 max-md:p-3 rounded-3xl justify-between text-sm max-md:min-w-[90%] max-md:snap-start md:my-5">
            <div className="flex gap-5 max-md:flex-col">

                <div className="w-fit max-md:flex max-md:items-center max-md:gap-2">
                    <div className="relative bg-gradient-to-t from-gray-100 to-transparent rounded-b-full p-4">
                        <Store className="text-gray-400" size={34} />
                        <BadgeCheck size={20} className="text-white absolute bottom-3 left-2" fill="#1F84FB" />
                    </div>
                    <span className="md:hidden">
                        سپهر پلاس
                    </span>

                </div>
                <div className="flex md:flex-col gap-1 max-md:gap-3 max-md:text-xs max-md:flex-wrap">
                    <span className="max-md:hidden">
                        سپهر پلاس
                    </span>
                    <p className="flex gap-3 text-sm">
                        <span className="text-yellow">67%</span>
                        <span className="border-l-2 pl-2">رضایت از کالا</span>
                        <span>عملکرد عالی</span>
                    </p>
                    <div className="flex items-center md:gap-2 md:hidden">
                        <Award size={22} />
                        <span>
                            گارانتی 18 ماهه
                        </span>
                    </div>
                </div>
            </div>
            <div className="flex items-center gap-2 max-md:hidden">
                <BaggageClaim size={22} />
                <span >
                    ارسال مشتری
                </span>
            </div>
            <div className="flex items-center gap-2 max-md:hidden">
                <Award size={22} />
                <span>
                    گارانتی 18 ماهه
                </span>
            </div>
            <div className="flex items-center gap-5 max-md:my-5">
                <p>
                    <strong>8,087,000</strong>
                    <span>تومان</span>
                </p>
                <div className="relative">
                    <SpecialPrice price={8233000} />
                    <span className="bg-red-500 text-white p-[2px] text-xs rounded-bl-full rounded-tl-full rounded-tr-full absolute left-5 -top-5">10%</span>
                </div>
            </div>
            <div className="flex items-center">
                <CustomButton className="px-5 py-4"> افزودن به سبد خرید </CustomButton>
            </div>
        </div>
    )
}

export default OtherCustomersItem