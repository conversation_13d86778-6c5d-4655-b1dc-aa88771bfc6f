'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';

const tabs = [
  { id: 'productDescription', label: 'توضیح محصول' },
  { id: 'productInfo', label: 'مشخصات' },
  { id: 'productHelp', label: 'راهنما' },
  { id: 'productComments', label: 'دیدگاه' },
  { id: 'productQuestionsAndAnswers', label: 'پرسش و پاسخ', hideOnMobile: true },
];

const ProductTabs = () => {
  const [activeTab, setActiveTab] = useState('');
  const [isSticky, setIsSticky] = useState(false);
  const tabRef = useRef<HTMLDivElement>(null);
  const placeholderRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      // Sticky logic
      const tabTop = placeholderRef.current?.offsetTop || 0;
      setIsSticky(window.scrollY >= tabTop);

      // Active tab logic
      let currentId = '';
      for (const tab of tabs) {
        const section = document.getElementById(tab.id);
        if (section) {
          const offsetTop = section.offsetTop;
          if (window.scrollY + 100 >= offsetTop) {
            currentId = tab.id;
          }
        }
      }
      setActiveTab(currentId);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); 

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <>
      {/* Spacer to prevent layout shift when sticky */}
      <div ref={placeholderRef} className=""></div>

      <div
        ref={tabRef}
        className={`bg-white rounded-3xl mb-8 z-40 transition-all px-3 ${
          isSticky ? 'fixed top-0 left-0 w-full shadow-md md:px-10 px-3 rounded-none' : ''
        }`}
      >
        <ul className="flex items-center md:gap-8 h-full py-6 max-md:text-sm max-md:justify-around">
          {tabs.map(tab => (
            <li key={tab.id} className={tab.hideOnMobile ? 'max-md:hidden' : ''}>
              <Link
                href={`#${tab.id}`}
                className={activeTab === tab.id ? 'active-tab' : ''}
              >
                {tab.label}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </>
  );
};

export default ProductTabs;
