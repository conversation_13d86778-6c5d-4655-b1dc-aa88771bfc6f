import Script from "next/script";

interface MotherSchemaProps {
  schema: string;
  id: string;
}

const MotherSchema = ({ schema, id }: MotherSchemaProps) => {
  let pageSchema: unknown;

  try {
    pageSchema = JSON.parse(schema);
  } catch (error) {
    console.error(`Invalid JSON schema in MotherSchema (id: ${id}):`, error);
    return null; 
  }

  if (!pageSchema || typeof pageSchema !== "object") return null;

  return (
    <Script
      id={`jsonld-mother-schema-${id}`}
      type="application/ld+json"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(pageSchema) }}
    />
  );
};

export default MotherSchema;
