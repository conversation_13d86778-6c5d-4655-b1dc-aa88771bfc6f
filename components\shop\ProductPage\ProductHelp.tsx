import AccordionWrapper from "./AccordionWrapper"
import { Info } from "lucide-react"
import { ProductHelpResponse } from "@/lib/types/product.types"

const ProductHelp = ({ productHelp }: { productHelp: ProductHelpResponse }) => {
    return (
        <section className='bg-white mt-10 p-3 rounded-3xl scroll-mt-28' id="productHelp">
            <AccordionWrapper title=' راهنما ' icon={<Info />} >
            {
                productHelp.data.map(item => <div className="my-8 product-help w-[96%] px-3" key={item.id} dangerouslySetInnerHTML={{ __html: item.content }}></div>)
            }
               
            </AccordionWrapper>
        </section>
    )
}

export default ProductHelp