import { IconProps } from "@/lib/types/types";


const LicensePlate = (props) => (
  <svg
    id="Layer_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 512 512"
    style={{
      enableBackground: "new 0 0 512 512",
    }}
    xmlSpace="preserve"
    {...props}
  >
    <g>
      <g>
        <path d="M373.492,229.763c-6.107,0-9.706,3.38-9.706,10.905v28.898c0,7.524,3.6,10.905,9.706,10.905s9.815-3.381,9.815-10.905 v-28.898C383.307,233.143,379.599,229.763,373.492,229.763z" />
      </g>
    </g>
    <g>
      <g>
        <path d="M435.539,229.763c-6.107,0-9.706,3.38-9.706,10.905v28.898c0,7.524,3.6,10.905,9.706,10.905s9.815-3.381,9.815-10.905 v-28.898C445.354,233.143,441.646,229.763,435.539,229.763z" />
      </g>
    </g>
    <g>
      <g>
        <path d="M311.445,229.763c-6.107,0-9.706,3.38-9.706,10.905v28.898c0,7.524,3.6,10.905,9.706,10.905s9.815-3.381,9.815-10.905 v-28.898C321.26,233.143,317.553,229.763,311.445,229.763z" />
      </g>
    </g>
    <g>
      <g>
        <path d="M460.688,164.532H51.312C23.019,164.532,0,187.55,0,215.843v80.314c0,28.293,23.019,51.312,51.312,51.312h409.377 c28.293,0,51.312-23.019,51.312-51.312v-80.314C512,187.55,488.981,164.532,460.688,164.532z M121.028,181.264 c6.161,0,11.155,4.994,11.155,11.155c0,6.161-4.994,11.155-11.155,11.155s-11.155-4.994-11.155-11.155 C109.874,186.258,114.868,181.264,121.028,181.264z M101.011,293.558L85.09,266.513l-15.922,27.044 c-0.763,1.199-2.29,1.745-4.035,1.745c-4.688,0-11.341-3.708-11.341-8.397c0-0.982,0.327-1.963,0.982-3.053l18.866-29.117 l-18.103-29.008c-0.764-1.199-1.091-2.29-1.091-3.381c0-4.579,6.215-8.069,11.014-8.069c2.398,0,4.035,0.872,5.126,2.836 l14.503,25.301l14.504-25.301c1.091-1.963,2.726-2.836,5.126-2.836c4.799,0,11.014,3.49,11.014,8.069 c0,1.092-0.327,2.182-1.091,3.381l-18.103,29.008l18.866,29.117c0.654,1.091,0.982,2.071,0.982,3.053 c0,4.689-6.653,8.397-11.342,8.397C103.301,295.302,101.666,294.757,101.011,293.558z M121.028,331.852 c-6.161,0-11.155-4.994-11.155-11.155c0-6.161,4.994-11.155,11.155-11.155s11.155,4.994,11.155,11.155 C132.183,326.858,127.189,331.852,121.028,331.852z M180.835,223.11l-22.466,37.405c-0.218,0.327-0.436,0.872-0.436,1.526v27.372 c0,3.489-4.253,5.234-8.507,5.234s-8.505-1.745-8.505-5.234v-27.372c0-0.654-0.218-1.199-0.437-1.526l-22.573-37.405 c-0.219-0.436-0.219-0.872-0.219-1.199c0-4.035,6.87-6.979,11.56-6.979c2.726,0,3.708,1.091,4.907,3.162l15.267,28.026 l15.158-28.026c1.092-2.071,2.182-3.162,4.907-3.162c4.689,0,11.56,2.945,11.56,6.979 C181.054,222.237,181.054,222.674,180.835,223.11z M233.942,294.648h-46.455c-3.49,0-4.907-1.854-4.907-4.471 c0-1.745,0.655-3.926,1.854-5.999l30.752-54.416h-25.301c-3.272,0-5.126-3.49-5.126-7.416c0-3.599,1.527-7.415,5.126-7.415h42.094 c3.272,0,4.906,1.854,4.906,4.472c0,1.744-0.654,3.816-1.853,5.998l-30.752,54.416h29.661c3.273,0,5.127,3.926,5.127,7.416 C239.067,290.722,237.213,294.648,233.942,294.648z M338.273,269.566c0,18.757-11.778,25.736-26.827,25.736 s-26.718-6.979-26.718-25.736v-28.898c0-18.757,11.669-25.736,26.718-25.736s26.827,6.979,26.827,25.736V269.566z  M346.775,269.566v-28.898c0-18.757,11.669-25.736,26.718-25.736s26.827,6.979,26.827,25.736v28.898 c0,18.757-11.778,25.736-26.827,25.736S346.775,288.324,346.775,269.566z M390.972,331.852c-6.161,0-11.155-4.994-11.155-11.155 c0-6.161,4.994-11.155,11.155-11.155c6.161,0,11.155,4.994,11.155,11.155C402.126,326.858,397.132,331.852,390.972,331.852z  M390.972,203.573c-6.161,0-11.155-4.994-11.155-11.155c0-6.161,4.994-11.155,11.155-11.155c6.161,0,11.155,4.994,11.155,11.155 C402.126,198.579,397.132,203.573,390.972,203.573z M462.366,269.566c0,18.757-11.778,25.736-26.827,25.736 c-15.049,0-26.718-6.979-26.718-25.736v-28.898c0-18.757,11.669-25.736,26.718-25.736c15.049,0,26.827,6.979,26.827,25.736 V269.566z" />
      </g>
    </g>
  </svg>
);
export default LicensePlate;
