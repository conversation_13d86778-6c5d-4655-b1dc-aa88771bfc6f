
import CallCookie from "@/components/common/CallCookie";

type Props = {
    searchParams: Promise<{
        reInquiry: string,
        traceNumber: string,
        token?: string
    }>
}

export default async function InquiryResultPage({ searchParams }: Props) {

    const { reInquiry, traceNumber, token } = await searchParams
    const isReInquiry = reInquiry === 'true'

    return (
        <>
             <CallCookie token={token} isReInquiry={isReInquiry} traceNumber={traceNumber} />            
        </>
    );
}
