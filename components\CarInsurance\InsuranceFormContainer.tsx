'use client'

import useInquiryPost from "@/lib/hooks/useInquiryPost";
import { useRouter } from 'nextjs-toploader/app';
import {useEffect, useRef} from "react";
import PlateInputCar, {carPlatePartsMaxLengths, PlateInputRef} from "@/components/UI/CarPlateInput";
import {useForm} from "react-hook-form";
import {
    InsuranceInquiryFormSchema,
    InsuranceInquiryType,
    ViolationInquiryType,
} from "@/lib/types/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import {
    CAR_PLATE_ALPHABET,
    CAR_PLATE_LEFT,
    CAR_PLATE_MIDDLE,
    CAR_PLATE_RIGHT,
    MOTOR_PLATE_LEFT,
    MOTOR_PLATE_RIGHT,
    NATIONAL_CODE_MAX_LENGTH, PHONE_NUMBER_MAX_LENGTH
} from "@/lib/constants";
import PlateInputMotor, {motorPlatePartsMaxLengths} from "@/components/UI/MotorPlateInput";
import {isValidIranianNationalCode, plateNumberIsNotValid} from "@/lib/validations";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/UI/form";
import Card from "@/components/common/Card";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import CustomInput from "@/components/UI/CustomInput";
import IdCardIcon from "@/components/common/svg/IdCardIcon";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CustomButton from "@/components/UI/CustomButton";
import StatusMessage from "@/components/common/StatusMessage";
import {ServiceStatusType} from "@/lib/types/types";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import InquiryInsuranceIcon from "@/components/common/svg/services/InquiryInsuranceIcon";
import {BookText} from "lucide-react";

type Props = {
    isMotor: boolean;
    status: ServiceStatusType
}

const CAR = 'خودرو';
const MOTOR_CYCLE = 'موتور سیکلت'

export default function InsuranceFormContainer({isMotor, status}: Props) {
    const {mutate, isLoading} = useInquiryPost();
    const router = useRouter()
    const vehicleType = isMotor ? MOTOR_CYCLE : CAR
    const nationalCodeRef = useRef<HTMLInputElement | null>(null)
    const insuranceNumberRef = useRef<HTMLInputElement | null>(null)
    const plateInputRef = useRef<PlateInputRef | null>(null)


    const form = useForm<InsuranceInquiryType>({
        mode: 'onSubmit',
        resolver: zodResolver(InsuranceInquiryFormSchema),
        defaultValues: {
            plateNumber: isMotor ?
                ["", ""] :
                ["", "", "", ""],
            nationalCode: "",
            insuranceNumber: ""
        },
    })

    useEffect(() => {
        const subscription = form.watch((values) => {

            const plateNumber = values.plateNumber;
            const nationalCode = values.nationalCode;
            const insuranceNumber = values.insuranceNumber;

            if (!nationalCode && ((!isMotor && plateNumber?.[CAR_PLATE_RIGHT]?.length === carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
                (isMotor && plateNumber?.[MOTOR_PLATE_RIGHT]?.length === motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT])
            )) {
                nationalCodeRef.current?.focus();
            }

            if (document.activeElement === nationalCodeRef.current && !insuranceNumber && nationalCode?.length === NATIONAL_CODE_MAX_LENGTH) {
                insuranceNumberRef.current?.focus();
            }
        });

        return () => subscription.unsubscribe(); // Cleanup on unmount
    }, [isMotor, form]); // `watch` reference is stable, so this won't cause extra re-renders.

    async function onSubmit(values: ViolationInquiryType) {
        if (isLoading || status === 'DISABLED' || status === 'COMING_SOON') return;

        if (plateNumberIsNotValid(values.plateNumber, isMotor)) {
            form.setError('plateNumber', {message: 'شماره پلاک را صحیح وارد کنید'})
            return
        }

        const plaque = form.getValues('plateNumber');
        const insuranceNumber = form.getValues('insuranceNumber');
        const nationalId = form.getValues('nationalCode')

        if (nationalId) {
            const isNationalCodeValid = isValidIranianNationalCode(nationalId)
            if (!isNationalCodeValid) {
                form.setError('nationalCode', {message: 'کد ملی را صحیح وارد کنید'})
                return;
            }
        }
        // const mutateResult = await mutate({
        //     withDetails: isWithInfo ? 'true' : 'false',
        //     phoneNumber: isWithInfo ? phone : "",
        //     nationalCode: isWithInfo ? nationalId : "",
        //     inquiry: 'true',
        //     middle: isMotor ? "" : plaque[CAR_PLATE_MIDDLE],
        //     left: isMotor ? plaque[MOTOR_PLATE_LEFT] : plaque[CAR_PLATE_LEFT],
        //     right: isMotor ? plaque[MOTOR_PLATE_RIGHT] : plaque[CAR_PLATE_RIGHT],
        //     alphabet: isMotor ? "" : plaque[CAR_PLATE_ALPHABET],
        //     isMotor: isMotor ? 'true' : 'false',
        //     reInquiry: 'false'
        // })
        //
        // if (!mutateResult.success && mutateResult.href) {
        //     router.push(mutateResult.href)
        // } else if (!mutateResult.success && mutateResult.message) {
        //     toast.error(mutateResult.message);
        // } else if (mutateResult.success && mutateResult.href) {
        //     router.push(mutateResult.href)
        // }
    }


    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="mt-5 md:mt-10"
                autoComplete="off"
            >
                <Card className='!px-0 !pt-5 !pb-10  mt-5'>
                    <InquiryHeader
                        icon={<div className='w-[50px] h-[50px] flex justify-center items-center'>
                            <DisplayServiceIcon
                                containerWidth={33}
                                containerHeight={33}
                                variant='green'
                                status='ACTIVE'
                                border={false}
                                shadow={false}
                            >
                                <InquiryInsuranceIcon width={25} height={25}/>
                            </DisplayServiceIcon>
                        </div>
                        }
                        title={`استعلام بیمه شخص ثالث ${vehicleType}`}/>
                    <div className='px-5 mt-2 md:px-14'>
                        <FormField
                            control={form.control}
                            name="plateNumber"
                            render={({field}) => (
                                <FormItem>
                                    <FormControl>
                                        {isMotor ? <
                                                PlateInputMotor
                                                {...field}
                                                ref={plateInputRef}
                                            /> :
                                            <PlateInputCar
                                                {...field}
                                                ref={plateInputRef}
                                            />}
                                    </FormControl>
                                    <FormMessage className='text-xs'/>
                                </FormItem>
                            )}
                        />
                        <div className='mt-8'>

                            <div>
                                <FormField
                                    control={form.control}
                                    name="nationalCode"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className='text-[#596068] text-xs'>{`کد ملی مالک ${vehicleType}`}</FormLabel>
                                            <FormControl>
                                                <CustomInput
                                                    variant='secondary'
                                                    allowOnlyNumbers
                                                    maxLength={NATIONAL_CODE_MAX_LENGTH}
                                                    placeholder='کد ملی مالک را وارد کنید'
                                                    {...field}
                                                    ref={nationalCodeRef}
                                                    leftIcon={<IdCardIcon height={20} width={20}/>}
                                                    direction='rtl'
                                                    inputMode='numeric'
                                                    autoFocus
                                                />
                                            </FormControl>
                                            <FormMessage className='text-xs'/>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className='mt-4'>
                                <FormField
                                    control={form.control}
                                    name="insuranceNumber"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className='text-[#596068] text-xs'>{`شماره بیمه مالک ${vehicleType}`}</FormLabel>
                                            <FormControl>
                                                <CustomInput
                                                    variant='secondary'
                                                    allowOnlyNumbers
                                                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                                                    leftIcon={<BookText width={20} height={20}
                                                                        className='text-gray-400'/>}
                                                    placeholder='شماره بیمه مالک را وارد کنید'
                                                    {...field}
                                                    ref={insuranceNumberRef}
                                                    inputMode='numeric'
                                                    direction='rtl'
                                                />
                                            </FormControl>
                                            <FormMessage className='text-xs'/>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <div className='mt-5'>
                                <ChoiceWrapper
                                    backgroundColor='#FFF5D8'
                                    borderColor='#F7BC06'
                                >
                                    <div className='flex py-3 flex-col gap-y-1'>
                                        <p className='text-[#5E646B] text-xs md:text-sm'>هزینه استعلام: 16,170 تومان</p>
                                        <p className='text-[#5E646B] text-xs md:text-sm'>خودراکس هیچ دخل و تصرفی در
                                            تعیین
                                            این هزینه
                                            ندارد</p>
                                    </div>
                                </ChoiceWrapper>
                            </div>

                            <CustomButton
                                loading={isLoading}
                                disabled={isLoading || status === 'DISABLED' || status === 'COMING_SOON'}
                                type='submit'
                                className='mt-5 !py-5'
                            >
                                استعلام
                            </CustomButton>
                            {(status === 'DISABLED' || status === 'COMING_SOON') && <div className='mt-5'>
                                <StatusMessage status={status}/>
                            </div>}
                        </div>
                    </div>

                </Card>
            </form>
        </Form>
    );
}
