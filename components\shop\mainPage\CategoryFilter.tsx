import { ChevronLeft, ChevronsLeft } from 'lucide-react'
import AccordionHeader from './AccordionHeader'

const CategoryFilter = () => {
    return (
        <div className='bg-white min-h-20 rounded-3xl p-3 search-products-filter '>
            <AccordionHeader title='دسته بندی'>
                <div className='mt-5'>
                    <ul className='flex flex-col gap-6 mb-3 text-sm'>
                        <li className='flex items-center justify-between relative'>
                           لوازم یدکی خودرو   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                        <li className='flex items-center justify-between relative'>
                            تایر و لستیک   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                        <li className='flex items-center justify-between relative'>
                            لوازم بدنه خودرو   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                        <li className='flex items-center justify-between relative'>
                            جک خودرو   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                        <li className='flex items-center justify-between relative'>
                            لوازم برقی خودرو   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                        <li className='flex items-center justify-between relative'>
                            لوازم یدکی خودرو   <div className='flex justify-end '> <ChevronLeft className='text-gray-400' /> <ChevronLeft className='text-gray-300 absolute -left-2 top-0' /> </div>
                        </li>
                    </ul>
                </div>

            </AccordionHeader>

        </div>
    )
}

export default CategoryFilter