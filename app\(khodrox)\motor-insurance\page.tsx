import Faq from '@/components/InquiryStaticComponents/Faq';
import envConfig from "@/lib/config-env";
import InsuranceComponent from "@/components/CarInsurance/InsuranceComponent";
import ChildSchema from '@/components/common/ChildSchema';
import { PageContentResponse } from '@/lib/types/types';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import { getPageContent } from '@/lib/utils';


export async function generateMetadata() {
    const data = await getPageContent("motor-insurance");
    
    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}


const isMotor: boolean = true
const env = envConfig()
const status = env.Services.THIRD_PARTY_INSURANCE_INQUIRY_SECTION
const page = async () => {
    const data: PageContentResponse = await getPageContent("motor-insurance")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='motor-insurance'
                    schema={schema}
                />
            }
            <InsuranceComponent
                title={title || ""}
                isMotor={isMotor}
                status={status}
            />
            {/* <MotorInsuranceAbout /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    )
}

export default page