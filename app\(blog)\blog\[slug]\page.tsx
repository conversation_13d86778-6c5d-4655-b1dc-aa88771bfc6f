
import BlogPic1 from "@/public/assets/images/blog-pic1.png";
import Ads from "@/public/assets/images/ads3.png";
import Image from "next/image";
import ArticleSlider from "@/components/blog/SinglePage/ArticleSlider";
import BlogHead from "@/components/blog/SinglePage/BlogHead";
import BlogPostCategory from "@/components/blog/SinglePage/BlogPostCategory";

import { AboutAuthor } from "@/components/blog/SinglePage/AboutAuthor";
import MobileShareSection from "@/components/blog/SinglePage/MobileShareSection";

import InstagramIcon from "@/components/common/svg/InstagramIcon";
import TelegramIcon from "@/components/common/svg/TelegramIcon";
import RecommendedPost from "@/components/blog/SinglePage/RecommendedPost";
import SocialCard from "@/components/common/SocialCard";
import BlogSearchBox from "@/components/blog/SinglePage/BlogSearchBox";
import BlogCategoriesList from "@/components/blog/SinglePage/BlogCategoriesList";
import NewestArticlesList from "@/components/blog/SinglePage/NewestArticlesList";
import { apiClient } from "@/lib/apiClient";
import { ArticleResponse, CateoryResponse } from "@/lib/types/article.types";

import type { Metadata } from 'next'
import ChildSchema from "@/components/common/ChildSchema";
import BlogMainContent from "@/components/blog/main/BlogMainContent";

// type Props = {
//   params: {
//     slug: string;
//   };
// }

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug: rawSlug } = await params;
  const slug = decodeURIComponent(rawSlug);

  const response = await apiClient(`article/view/${slug}`);
  const { data: article } = await response.json() as ArticleResponse;

  return {
    title: article.meta_title || article.title,
    description: article.meta_description || '',
    keywords: article.tags || [],
    ...(article.meta_search && { robots: article.meta_search }),
    ...(article.canonical && { alternates: { canonical: article.canonical } }),
  };
}



// type PageProps = {
//   params: {
//     slug: string;
//   };

// }


const BlogPostPage = async ({
  params,
}: {
  params: Promise<{ slug: string }>;
}) => {
  const { slug: rawSlug } = await params;
  const slug = decodeURIComponent(rawSlug);

  // Decode the URL-encoded Persian characters
  const decodedSlug = decodeURIComponent(slug)

  // Log the decoded slug to verify it's correct
  console.log("Decoded slug for API call:", decodedSlug)

  // Use the decoded slug in the API call
  const response = await apiClient(`article/view/${decodedSlug}`)

  // Parse the JSON response
  const articleData: ArticleResponse = await response.json()
  const CategoriesResponse: CateoryResponse = await apiClient("categories")
      .then(res => res.json())
  // Access article data using the typed interface
  const article = articleData.data
  // Check if article.ads exists and if any of its properties are truthy
  const articleAds = article.ads && Boolean(article.ads.cover || article.ads.description || article.ads.title || article.ads.link)

  console.log( article)
  const schema = article.schema


  return (
    <>
      {
        article.type == "article" ?
      <>
        {
          schema &&
          <ChildSchema
            id={decodedSlug}
            schema={schema}
          />
        }
        <div className=" bg-gradient-to-b from-white to-[#F5F6F8] ">
          <div className="min-md:h-[85vh] relative ">
            <div className="bg-[url('/assets/images/bg-gray.webp')] bg-bottom bg-no-repeat bg-cover h-96 max-md:h-80  top-0 absolute left-0 w-full"></div>
            <div className="container mx-auto flex justify-between">
              <div className={articleAds ? "md:w-[73%]" : "md:max-w-[950px] mx-auto"}>
                <BlogHead {...articleData.data} />
                <div className=" max-md:hidden mb-10 pb-10  mx-auto mt-8 max-md:mt-5">
                  <BlogPostCategory category={article.category} />
                  {/* <div className="desc-section container mx-auto mt-5 max-md:mt-1 p-2">
                    <h2 className="md:text-xl mb-5 max-md:leading-7">
                      لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ
                    </h2>
                    <p className="text-justify leading-8 max-md:text-sm max-md:leading-7">
                      لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.
                    </p>
                  </div> */}
                  {/* <BlogPostTableOfContent /> */}
                  {/* <div className=" mx-auto mt-8">
                    <div className=" mx-auto w-full max-md:max-w-[96%]">
                      <Image
                        src={BlogPic1}
                        alt=""
                        width={1200}
                        height={800}

                        className="object-contain"
                        priority
                      />
                    </div>
                  </div> */}
                  {/* <div className="my-5 p-1 max-md:px-3">
                    <h2 className="mb-5 md:text-xl">
                      اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد
                    </h2>
                    <p className="text-justify max-md:text-sm max-md:leading-7 leading-8">
                      لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.
                    </p>
                  </div> */}
                </div>
                {/* <div className="bg-[#F5F6F8]  mx-auto max-md:w-[96%] max-md:mx-auto px-5 rounded-3xl py-8 max-md:py-5 bg-[url('/assets/images/double-comma.png')] bg-no-repeat bg-[length:100px_auto] max-md:bg-[length:110px_auto] max-md:bg-[position:40px_90px] bg-[position:90px_40px]">
                  <h2 className="relative md:text-lg mb-5 pr-5 before:content-[''] before:absolute before:top-[0.3em] before:right-0 before:w-[0.6em] before:h-[0.6em] before:border-[2px] before:border-yellow before:rounded-full before:bg-transparent inline-block">
                    اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد
                  </h2>
                  <p className="text-justify max-md:text-sm max-md:leading-7 leading-8">
                    لورم ایپسوم مَتْنَ  ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد.

                  </p>
                </div> */}
                {/* <div className="my-5  mx-auto p-1 max-md:px-3">
                  <h2 className="mb-5 md:text-xl">
                    اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد
                  </h2>
                  <p className="text-justify max-md:text-sm max-md:leading-7 leading-8">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.
                  </p>
                </div> */}
                {/* <LearnMore /> */}
                {/* <div className="p-1  mx-auto mt-5 max-md:px-3">
                  <p className="text-justify leading-8 max-md:text-sm max-md:leading-7">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.
                  </p>

                  <h2 className="md:text-xl mb-5 mt-7">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم
                  </h2>
                  <p className="text-justify leading-8 max-md:text-sm max-md:leading-7">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد و زمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.
                  </p>
                </div> */}
                <section className='SeoSection article-section max-md:mt-5 about-service p-3.5 mx-auto md:max-w-7xl pt-5' dangerouslySetInnerHTML={{ __html: article.description }}>

                </section>
                <div className=" mx-auto mt-10">
                  {/* <Faq faqs={faqs} className="mt-10 mb-10" /> */}
                  <AboutAuthor authorAvatar={article.author_avatar || ""} authorAbout={article.author_about} authorFullName={article.author_fullname} />
                  <section className="weblog-category md:hidden mt-5 bg-yellow max-md:w-[96%] font-bold md:container flex items-center justify-between mx-auto text-black px-5 py-3 rounded-3xl">
                    <MobileShareSection />

                  </section>
                  <ArticleSlider />

                </div>
                {
                  article.comments.length &&
                  <div className=" mx-auto mb-10">
                    {/* <UserComments /> */}
                  </div>
                }
                {/* <div className=" mx-auto mb-10">
                  <UserComments />
                </div> */}
              </div>

              {
                article.sidebar_right &&
                <div className="max-md:hidden bg-white p-3 w-[25%] h-fit mt-16 z-[2] rounded-3xl shadow-md">
                  <BlogSearchBox />


                  <RecommendedPost
                    image={BlogPic1}
                    title="لورم ایپسوم متن ساختگی تیم طراحی سپهر پلاس برای قسمت عنوان وبلاگ"
                  />

                  <BlogCategoriesList />
                  {
                    articleAds &&
                    <div className="relative mx-auto w-full max-md:max-w-[100%] mt-4 h-[500px] rounded-xl overflow-hidden">
                      <Image
                        src={article.ads?.cover || Ads}
                        alt="Advertisement"
                        fill
                        className="object-contain"
                        priority
                      />
                    </div>
                  }

                  <NewestArticlesList />
                  {/* TODO: this part is better to use Image not html */}
                  {/* <div className=" mt-10 flex flex-col gap-3">
                    <SocialCard
                      platformName="Instagram"
                      username="Khodroxcom@"
                      gradientFrom="#FFBF4B"
                      gradientTo="#F6406D"
                      icon={<InstagramIcon className="text-blue-500" size={40} />}
                    />

                    <SocialCard
                      platformName="Telegram"
                      username="Khodroxcom@"
                      gradientFrom="#36AEE2"
                      gradientTo="#1D93D1"
                      icon={<TelegramIcon className="text-blue-500" width={40} />}
                    />


                  </div> */}

                </div>
              }
            </div>

          </div>


        </div>

      </>
      :
      <BlogMainContent articleDescription={article.description} articleTitle={article.title_article} ArticlesResponse={article.articles || []} CategoriesResponse={CategoriesResponse} />
      }
    
    </>
  )
}

export default BlogPostPage