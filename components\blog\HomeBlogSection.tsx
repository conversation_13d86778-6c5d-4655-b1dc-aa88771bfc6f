import HomeSection from "@/components/Header/HomeSection";
import BlogSectionHead from "@/components/blog/BlogSectionHead";
import BlogSlider from "@/components/blog/BlogSlider";
import {getBlogPosts} from "@/actions/blog.action";
import { ArticlesApiResponse } from "@/lib/types/types";
import { apiClient } from "@/lib/apiClient";

export default async function HomeBlogSection() {

     const response:ArticlesApiResponse = await apiClient("articles")
      .then(res => res.json())
    

    // if (!response.success) {
    //     console.error(response.message);
    //     return;
    // }

    const blogs = response?.data ?? []

    return (
        <HomeSection>
            <div className='flex flex-col gap-y-5 md:gap-y-8'>
                <BlogSectionHead/>
                {blogs.length === 0 &&
                    <div className='w-full h-[150px] rounded-2xl flex justify-center items-center bg-white'>
                        <p>در حال حاضر هیچ پستی در بلاگ موجود نیست</p>
                    </div>}
                {blogs.length > 0 && <BlogSlider blogPosts={blogs}/>}
            </div>
        </HomeSection>
    );
}
