
import Image from 'next/image';
import White from "@/public/assets/images/white.png";
import Green from "@/public/assets/images/green.png";
import Yellow from "@/public/assets/images/yellow.png";
import Blue from "@/public/assets/images/blue.png";
import AccordionHeader from './AccordionHeader';

const SelectColor = () => {
  // const scrollRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   const el = scrollRef.current;
  //   if (!el) return;

  //   const handleWheel = (e: WheelEvent) => {
  //     if (e.deltaY === 0) return;
  //     e.preventDefault();
  //     el.scrollTo({
  //       left: el.scrollLeft + e.deltaY,
  //       behavior: 'smooth',
  //     });
  //   };

  //   el.addEventListener('wheel', handleWheel, { passive: false });

  //   return () => el.removeEventListener('wheel', handleWheel);
  // }, []);

  return (
    <div className="bg-white min-h-20 rounded-3xl p-3 search-products-filter">
      <AccordionHeader title="انتخاب رنگ">
        <div  className="max-md:overflow-y-hidden max-md:overlow-x-auto ">
          <div className="flex gap-6 max-md:w-max md:w-full md:flex-wrap md:justify-between md:px-2 md:pb-4">
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="white" src={White} />
              </div>
              <span>سفید</span>
            </div>
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="Blue" src={Blue} />
              </div>
              <span>آبی</span>
            </div>
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="Yellow" src={Yellow} />
              </div>
              <span>زرد</span>
            </div>
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="Green" src={Green} />
              </div>
              <span>سبز</span>
            </div>
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="Green" src={Green} />
              </div>
              <span>سبز</span>
            </div>
            <div className="flex flex-col gap-2 items-center justify-center">
              <div className="border-2 border-dashed rounded-full p-1">
                <Image alt="Green" src={Green} />
              </div>
              <span>سبز</span>
            </div>
           
          </div>
        </div>
      </AccordionHeader>
    </div>
  );
};

export default SelectColor;
