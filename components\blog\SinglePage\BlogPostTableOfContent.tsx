import AccordionWrapper from '@/components/shop/ProductPage/AccordionWrapper'
import { FileText } from 'lucide-react'

const BlogPostTableOfContent = () => {
  return (
    <section className="container mt-5 mx-auto shadow-sm border p-3 px-5 md:rounded-2xl">

              <AccordionWrapper fullBorderBottom title="فهرست مطالب" roundedArrow icon={<FileText />}>
                <div>
                  <div>
                    <h3 className="hollow-circle pr-5 md:text-lg">1-اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد</h3>
                    <ul className="pr-6 flex flex-col gap-3 mt-2 max-md:mt-3 text-base max-md:text-sm ">
                      <li>
                        -1/1- اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد
                      </li>
                      <li>
                        -1/2- اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد
                      </li>
                    </ul>
                  </div>
                  <div className="mt-5 pr-5 md:text-lg hollow-circle">
                    <h3>2-اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد</h3>
                  </div>
                  <div className="mt-5 pr-5 md:text-lg hollow-circle">
                    <h3>3-اسم و عنوان فهرست محتوا مادر اینجا قرار میگرد</h3>
                  </div>
                </div>
              </AccordionWrapper>

            </section>
  )
}

export default BlogPostTableOfContent