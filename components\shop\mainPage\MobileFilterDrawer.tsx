import CategoryFilter from './CategoryFilter'
import PriceRange from './PriceRange'
import AvailableProductsFilter from './AvailableProductsFilter'
import WarrantyFilter from './WarrantyFilter'
import SelectBrand from './SelectBrand'
import SelectColor from './SelectColor'
import ConditionalFilter from './ConditionalFilter'

const MobileFilterDrawer = () => {
    return (
        <div className="fixed mt-8 inset-0 z-50 bg-white">
            {/* Scrollable content */}
            <div className="h-full overflow-y-auto px-3 pb-10 pt-5">
                <PriceRange />

                <div className="mb-8">
                    <CategoryFilter />
                </div>

               

                <div className="mt-6">
                    <SelectBrand />
                </div>

                <div>
                    <SelectColor />
                </div>

                <div>
                    <ConditionalFilter />
                </div>

                <div className="my-1">
                    <AvailableProductsFilter />
                </div>

                <div>
                    <WarrantyFilter />
                </div>
            </div>
        </div>
    );
};


export default MobileFilterDrawer