import { LayoutList, ChartSpline, ScanLine, Share2, BellRing, Heart } from "lucide-react"
import ProductGallery from './ProductGallery'
import { Gallery } from "@/lib/types/product.types";
const CatalogProductCard = ({gallery}: {gallery: Gallery[]}) => {
    console.log(gallery);
    
  return (
    <div className='flex flex-col md:w-[30%] bg-[#F9FAFB] rounded-3xl'>
    <div className=''>
        <ul className='flex product-options justify-around my-5 '>
            <li>
                <LayoutList />
            </li>
            <li>
                <ChartSpline />
            </li>
            <li>
                <ScanLine />
            </li>
            <li>
                <Share2 />
            </li>
            <li>
                <BellRing />
            </li>
            <li>
                <Heart />
            </li>
        </ul>
    </div>
    <div className="max-md:w-full">
        <ProductGallery images={gallery} />

    </div>
</div>
  )
}

export default CatalogProductCard