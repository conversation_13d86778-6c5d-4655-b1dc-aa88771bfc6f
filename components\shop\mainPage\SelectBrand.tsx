import React from 'react'
import AccordionHeader from './AccordionHeader'

const SelectBrand = () => {
  return (
    <div className='bg-white min-h-20 rounded-3xl p-3 search-products-filter'>
      <AccordionHeader title='انتخاب برند'>
        <div className='mt-5'>
          <ul className='flex flex-col gap-5 text-sm font-light'>
            <div className="flex justify-between items-center mb-2">
              <div className='flex items-center'>
                <div className="custom-checkbox px-0">
                  <label className="flex items-center gap-2 text-sm font-medium">
                    <input type="checkbox" className="" id="iranian_products" />
                    کویر تایر
                  </label>
                </div>
              </div>
              <p>123</p>
            </div>
           
            <div className="flex justify-between items-center mb-2">
              <div className='flex items-center'>
                <div className="custom-checkbox px-0">
                  <label className="flex items-center gap-2 text-sm font-medium">
                    <input type="checkbox" className="" id="iranian_products" />
                    کویر تایر
                  </label>
                </div>
              </div>
              <p>123</p>
            </div>
           
            <div className="flex justify-between items-center mb-2">
              <div className='flex items-center'>
                <div className="custom-checkbox px-0">
                  <label className="flex items-center gap-2 text-sm font-medium">
                    <input type="checkbox" className="" id="iranian_products" />
                    کویر تایر
                  </label>
                </div>
              </div>
              <p>123</p>
            </div>
           



          </ul>
        </div>
      </AccordionHeader>
    </div>
  )
}

export default SelectBrand