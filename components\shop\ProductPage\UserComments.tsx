import { MessageCircle } from "lucide-react"
import AccordionWrapper from "./AccordionWrapper"
import CommentTextArea from "./CommentTextArea"

import CommentItem from "./CommentItem"
import { ProductComment } from "@/lib/types/product.types"

const UserComments = ({productComments}: {productComments: ProductComment[]}) => {
    return (
        <section className="bg-white rounded-3xl p-5 mt-10 scroll-mt-28" id="productComments">
            <AccordionWrapper title="نظرات مشتریان" icon={<MessageCircle />} roundedArrow={false}>
                <div className="mt-5">
                    <CommentTextArea />
                    <div>
                        <h4 className="mb-5">
                            {productComments.length} امتیاز و دیدگاه کاربران
                        </h4>
                        {
                            productComments.length && productComments.map(item => <CommentItem key={item.id} comment={item} />)
                        }
                       {/* <CommentItem /> */}
                       {/* <CommentItem />
                       <CommentItem /> */}
                    </div>
                </div>
            </AccordionWrapper>
        </section>
    )
}

export default UserComments