import {ChangeEvent} from "react";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import {cn} from "@/lib/utils";

type Props = {
    label: string,
    color?: string,
    name: string,
    checked?: boolean,
    onChange?: (e: ChangeEvent<HTMLInputElement>) => void
    onClick?: (value: string) => void,
    value: string,
}

export default function ChoiceItem({color = "#C9D0D9", checked, onChange, onClick, value, label, name}: Props) {
    return (
        <ChoiceWrapper
            onClick={() => onClick?.(value)}
            variant='normal'
            className='cursor-pointer'
            selected={checked}>
            <div className='py-[15px] md:py-[20px] flex flex-items gap-x-2'>
                <label className="relative flex items-center cursor-pointer">
                    <input
                        type="checkbox"
                        value={value}
                        id={`choice-item-${value}`}
                        checked={checked}
                        name={name}
                        readOnly
                        aria-label="inquiry option"
                        className={cn('appearance-none w-4 h-4 border bg-white checked:bg-[#2DC058] checked:border-[#2DC058] transition-all rounded-full cursor-pointer', {
                            " border-[#9DA5B0]": !checked,
                        })}
                    />
                    {checked && (
                        <svg
                            className="absolute left-1/2 top-1/2 w-3 h-3 text-white transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7"/>
                        </svg>
                    )}
                </label>
                <label
                    className={cn('text-black text-xs whitespace-nowrap md:text-sm font-medium cursor-pointer', {
                        'text-[#596068]': !checked,
                    })}>{label}</label>
            </div>
        </ChoiceWrapper>
    );
}
