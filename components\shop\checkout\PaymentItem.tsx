'use client';
import { useEffect, useRef } from 'react';
import WalletIcon from "@/components/common/svg/WalletIcon";
import CreditCardIcon from "@/components/common/svg/CreditCardIcon";
import clsx from 'clsx';
import { useAuth } from '@/lib/hooks/useAuth';
import { formatWithComma } from '@/utils/helpers';

type PaymentItemProps = {
  id: string;
  selected: string | null;
  onSelect: (id: string) => void;
};

export default function PaymentItem({ id, selected, onSelect }: PaymentItemProps) {
  const checkboxRef = useRef<HTMLInputElement | null>(null);
  const isChecked = selected === id;
  const { userData } = useAuth();

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if ((e.target as HTMLElement).closest('label')) return;
    onSelect(id);
  };

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.checked = isChecked;
    }
  }, [isChecked]);

  const isWallet = id === 'wallet';
  const walletBalance = userData?.balance || 0;

  return (
    <div
      onClick={handleClick}
      className={clsx(
        "mt-3 text-gray-400 bg-[#F9FAFB] p-3 rounded-3xl border cursor-pointer transition-all duration-300",
        isChecked
          ? "border-[#3c53c7] shadow-md scale-[1.01] bg-white"
          : "border-gray-200 hover:border-[#3c53c7]"
      )}
    >
      <div className="payment-method-item flex justify-between items-center">
        <div className="flex gap-5 items-center">
          <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent px-4 py-6 rounded-full">
            {isWallet ? <WalletIcon /> : <CreditCardIcon className="text-[#5e646b]" />}
          </div>
          <div className="flex flex-col gap-3">
            <h4 className='max-md:text-sm'>{isWallet ? 'کیف پول خودراکس' : 'پرداخت آنلاین'}</h4>
            {isWallet && (
              <div className="text-sm max-md:text-xs">
                <p>
                  <span className="ml-3">موجودی :</span>
                  <span className="text-base max-md:text-sm ml-2 max-md:ml-1 text-[#3EA75D] font-black">
                    {formatWithComma(walletBalance.toString())}
                  </span>
                  <span className="text-xs">تومان</span>
                  <span className="mx-2 max-md:hidden"> | </span>
                  <span className="text-gray-400 max-md:hidden">افزایش موجودی</span>
                </p>
              </div>
            )}
            {!isWallet && (
              <div className="text-sm max-md:text-xs">
                <p>
                  <span>پرداخت از طریق درگاه بانکی</span>
                </p>
              </div>
            )}
          </div>
        </div>
        <div>
          <div className="checkbox-wrapper-15">
            <input
              ref={checkboxRef}
              className="inp-cbx"
              id={`cbx-${id}`}
              type="checkbox"
              style={{ display: "none" }}
            />
            <label className="cbx" htmlFor={`cbx-${id}`}>
              <span>
                <svg width="12px" height="9px" viewBox="0 0 12 9">
                  <polyline points="1 5 4 8 11 1"></polyline>
                </svg>
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
