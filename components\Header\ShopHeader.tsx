"use client";
import Link from 'next/link';
import React, { useState, useEffect, useRef } from 'react';
import Logo from '@/public/assets/images/logo.png';
import Image from 'next/image';
import CustomButton from '../UI/CustomButton';
import UserIcon from '../common/svg/UserIcon';
import MenuIcon from '../common/svg/MenuIcon';
import ProfileIcon from '../common/svg/ProfileIcon';
import { LOGIN_PATH } from "@/lib/routes";
import { useAuth } from "@/lib/hooks/useAuth";
import UserLoggedInButton from "@/components/Header/UserLoggedInButton";
import NavbarItems from './NavbarItems';
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import CartIcon from '../common/svg/CartIcon';
import { HeartIcon, SearchIcon, SlidersHorizontal } from 'lucide-react';
import { useServiceStatus } from '@/lib/providers/ServicesProvider';
import { getMenuItems } from '@/utils/menuItems';
import CategoryMenu from './CategoryMenu';
import { useCart } from '@/lib/context/cart-context';
import CartPopup from '@/components/Header/CartPopup';

const ShopHeader = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false); // State for mobile menu
    const [isCartPopupOpen, setIsCartPopupOpen] = useState(false); // State for cart popup
    const menuRef = useRef<HTMLDivElement>(null); // Ref for the menu
    const cartRef = useRef<HTMLDivElement>(null); // Ref for the cart popup
    const { userData, logoutUser } = useAuth()
    const pathname = usePathname();
    const { data } = useServiceStatus()
    const { cartItems } = useCart()

    const services_status = data?.data?.services || {}
    // console.log(services_status);

    const menuItems = getMenuItems(services_status)

    // Close the menu and cart popup when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsMenuOpen(false);
            }
            if (cartRef.current && !cartRef.current.contains(event.target as Node)) {
                setIsCartPopupOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <header className='shadow-md z-10 relative bg-white max-md:px-3'>
            <nav
                className='w-full flex justify-between items-center gap-x-4 h-20 container  mx-auto'>
                {/* Logo and Burger Menu (for mobile) */}
                <div className='flex gap-4 items-center'>
                    <div className='h-full flex items-center gap-4'>
                        {/* Burger Menu Icon (visible on mobile) */}
                        <button
                            className='md:hidden'
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                        >
                            <MenuIcon />
                        </button>

                        <div className='block md:hidden h-[20px] w-[2px] bg-gray-200'></div>

                        {/* Logo */}
                        <div className="h-full w-36 md:w-40 lg:w-44">
                        <Link className="h-full w-full flex justify-center items-center" href="/">
                            <Image
                                src={Logo}
                                alt="logo"
                                width={128}
                                height={64} // 2:1 ratio
                                className="object-contain w-full h-auto" // maintain aspect ratio
                                priority
                            />
                        </Link>
                    </div>
                    </div>
                    <div className="hidden md:flex  items-center  min-w-[580px] gap-4">
                        <CategoryMenu />
                        <div className='w-full relative'>
                            <input
                                type="text"
                                placeholder="جستجو..."
                                className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100 rounded-full outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                            />
                            <SearchIcon className="absolute right-4 top-1/2 w-5 -translate-y-1/2 text-gray-400 text-lg" />
                            <SlidersHorizontal className='absolute left-5 w-5 top-1/2 -translate-y-1/2 text-gray-400 text-lg' />
                        </div>

                    </div>

                </div>

                {/* Navigation Links  */}
                <div
                    ref={menuRef}
                    className={`fixed md:static top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-200 ease-in z-50 ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'
                        } md:translate-x-0 md:shadow-none md:w-auto md:bg-transparent`}
                >
                    <ul className='md:h-full hidden  md:items-center flex-col md:flex-row justify-between md:gap-x-5 lg:gap-x-10 p-3 md:p-0'>
                        {
                            menuItems
                                .filter(item => item.status === 'ACTIVE' && (item.type === 'desktop' || item.type === 'both'))
                                .map((item, index) => (
                                    <li
                                        key={index}
                                        className='group'
                                    >
                                        <Link
                                            className={cn('block w-full h-full whitespace-nowrap text-[#363A3E] transition-all duration-75 text-xs font-light md:text-sm lg:text-base')}
                                            href={item.href}>
                                            {item.title}
                                            <div
                                                className={cn('h-[3px] w-full mt-1 transition-all duration-75 bg-transparent group-hover:bg-yellow rounded-3xl', {
                                                    'bg-yellow': pathname === item.href
                                                })}></div>
                                        </Link>
                                    </li>

                                ))
                        }
                    </ul>
                    <div className='md:hidden mt-10'>
                        <NavbarItems onClick={() => setIsMenuOpen(false)} />

                    </div>
                </div>

                {/* Backdrop for mobile menu (visible only on mobile) */}
                {isMenuOpen && (
                    <div
                        className='fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden'
                        onClick={() => setIsMenuOpen(false)}
                    />
                )}

                {/* User and Cart Icons */}
                <div className='ml-2 flex gap-4 items-center'>
                    <button
                        className="hidden md:flex border-2 p-2.5 hover:bg-gray-200 transition-all rounded-full items-center justify-center w-[2.8rem] h-[2.8rem]">
                        <HeartIcon className="md:size-6 size-4" />
                    </button>
                    <div className="relative" ref={cartRef}>
                        <button
                            onClick={() => setIsCartPopupOpen(!isCartPopupOpen)}
                            className="relative hidden md:flex border-2 p-2.5 hover:bg-gray-200 transition-all rounded-full items-center justify-center w-[2.8rem] h-[2.8rem]"
                        >
                            <CartIcon className="size-7" />
                            <span className="absolute top-0 right-1">
                                <span
                                    className="bg-red-500 flex items-center justify-center p-0.5 w-4 h-4 text-sm rounded-full text-white">
                                    {cartItems.map(Item => Item.quantity).reduce((acc, curr) => acc + curr, 0)}
                                </span>
                            </span>
                        </button>

                        {/* Cart Popup */}
                        {isCartPopupOpen && (
                            <CartPopup
                                cartItems={cartItems}
                                onClose={() => setIsCartPopupOpen(false)}
                            />
                        )}
                    </div>

                    {userData === null && (
                        <>
                            <span className='md:!block !hidden'>
                                <CustomButton href={LOGIN_PATH} bgColor='bg-primary'
                                    className='min-w-[125px] whitespace-nowrap gap-2 rounded-3xl'>
                                    <div className="flex items-center gap-1">
                                        <UserIcon className='' />
                                        <span>ورود به حساب</span>
                                    </div>
                                </CustomButton>
                            </span>
                            <Link href={LOGIN_PATH} className='mr-2 md:hidden'><ProfileIcon /></Link>
                        </>
                    )
                    }
                    {
                        userData && (
                            <UserLoggedInButton user={userData!} logOutUser={logoutUser} />
                        )
                    }

                </div>
            </nav>
        </header>
    );
};

export default ShopHeader;