"use client";
import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/UI/dropdown-menu";

const SendAsDropdown = () => {
  const [selected, setSelected] = useState('ارسال با نام شما');

  const handleSelect = (value: string) => {
    setSelected(value);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="rounded-xl border px-4 py-2 text-sm text-gray-500 hover:bg-gray-100 transition">
          {selected}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="text-right">
        <DropdownMenuItem onClick={() => handleSelect('ارسال با نام شما')}>
          ارسال با نام شما
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSelect('ارسال ناشناس')}>
          ارسال ناشناس
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SendAsDropdown;
