import PlateInputMotor from "@/components/UI/MotorPlateInput";
import PlateInputCar from "@/components/UI/CarPlateInput";

type Props = {
    left: string;
    right: string;
    middle: string | undefined;
    alphabet: string | undefined;
    isMotor: boolean;
}


export default function DisplayPlaque({left, middle, alphabet, right, isMotor}: Props) {
    return (
        <>
            {
                isMotor ? (
                    <PlateInputMotor readOnly value={[left, right]}/>
                ) : (<PlateInputCar readOnly value={[left, alphabet!, middle!, right]}/>)
            }
        </>
    );
}
