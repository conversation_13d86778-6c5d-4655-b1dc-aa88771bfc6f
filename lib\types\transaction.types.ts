export interface ProductTransactionData {
  payable_id: string;
  payable_type: string;
  id: string;
  user_id: string;
  status: string;
  track_code: string | null;
  amount: number;
  payment_method: string;
  payment_gateway: string;
  authority: string;
  ref_id: number;
  paid_at: string;
  source: string | null;
  terminal_id: string | null;
  card_number: string;
  card_hash: string;
  fee: number;
  fee_type: string;
  shaparak_fee: number;
  order_id: string | null;
  wages: string | null;
  code: number;
  created_at: string;
}

export interface ProductTransactionResponse {
  success: boolean;
  message: string;
  status: number;
  data: ProductTransactionData;
}
