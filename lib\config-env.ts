import {EnvConfig, ServiceStatusType} from "@/lib/types/types";


const envConfig = (): EnvConfig => {
    return {
        BASE_URL: process.env.BASE_URL || "",
        NODE_ENV: process.env.NODE_ENV || "development",
        X_APPLICATION_TOKEN: process.env.X_APPLICATION_TOKEN || "",
        Services: {
            VEHICLE_VIOLATION_SECTION: (process.env.NEXT_PUBLIC_VEHICLE_VIOLATION_SECTION as ServiceStatusType) || 'DISABLED',
            MOTORCYCLE_VIOLATION_SECTION: (process.env.NEXT_PUBLIC_MOTORCYCLE_VIOLATION_SECTION as ServiceStatusType) || 'DISABLED',
            DRIVING_VIOLATION_IMAGE_SECTION: (process.env.NEXT_PUBLIC_DRIVING_VIOLATION_IMAGE_SECTION as ServiceStatusType) || 'DISABLED',
            THIRD_PARTY_INSURANCE_INQUIRY_SECTION: (process.env.NEXT_PUBLIC_THIRD_PARTY_INSURANCE_INQUIRY_SECTION as ServiceStatusType) || 'DISABLED',
            LICENSE_DRIVER_STATUS_SECTION: (process.env.NEXT_PUBLIC_LICENSE_DRIVER_STATUS_SECTION as ServiceStatusType) || 'DISABLED',
            VEHICLE_CARD_AND_DOCUMENT_STATUS_SECTION: (process.env.NEXT_PUBLIC_VEHICLE_CARD_AND_DOCUMENT_STATUS_SECTION as ServiceStatusType) || 'DISABLED',
            NEGATIVE_SCORE_INQUIRY_SECTION: (process.env.NEXT_PUBLIC_NEGATIVE_SCORE_INQUIRY_SECTION as ServiceStatusType) || 'DISABLED',
            LICENSE_PLATE_HISTORY_INQUIRY_SECTION: (process.env.NEXT_PUBLIC_LICENSE_PLATE_HISTORY_INQUIRY_SECTION as ServiceStatusType) || 'DISABLED',
            FREEWAY_TOLL_SECTION: (process.env.NEXT_PUBLIC_FREEWAY_TOLL_SECTION as ServiceStatusType) || 'DISABLED',
            BUYING_BODY_INSURANCE_SECTION: (process.env.NEXT_PUBLIC_BUYING_BODY_INSURANCE_SECTION as ServiceStatusType) || 'DISABLED',
        }
    }
}


export default envConfig