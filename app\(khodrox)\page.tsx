import HeroSection from "@/components/Header/HeroSection";
import MainServices from "@/components/Services/MainServices";
import Services from "@/components/Services/Services";
import HomeBlogSection from "@/components/blog/HomeBlogSection";
import ChildSchema from "@/components/common/ChildSchema";
// import SendNotification from "@/components/common/SendNotification";
import { apiClient } from "@/lib/apiClient";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";

export const revalidate = 0

export async function generateMetadata() {
    const data = await getPageContent("landing-page");
    
    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags ?? [],
    };
}

export default async function HomePage() {
    const response = await (await apiClient(`services`, {
        next: { revalidate: 60 }
    })).json()
    const services_status = response?.data?.services
    const data: PageContentResponse = await getPageContent("landing-page")

    
    const { schema } = data
    
    


    return (
        <>
        {
            schema && 
            <ChildSchema
               schema={schema}
               id="landing-page"
            />
        }
        {/* <SendNotification /> */}
            <main className="min-w-screen">
                <div className='w-full'>
                    <div className="flex flex-col">
                        <section className='bg-pattern py-5 md:py-10'>
                            <HeroSection />
                        </section>
                        <div className='bg-white'>
                            <section id="services" className='py-5 md:py-10'>
                                <Services />
                            </section>
                            <section className='py-5 md:py-10'>
                                <MainServices />
                            </section>
                        </div>
                        {/*<ProductsSlider/>*/}
                        {
                            services_status?.blog == "ACTIVE" &&
                            <section className='bg-[#F5F6F8] py-5 md:py-10'>
                                <HomeBlogSection />
                            </section>
                        }
                        
                           
                            {/* <section className='bg-[#F5F6F8] py-5 md:py-10'>
                                <HomeBlogSection />
                            </section> */}
                        
                    </div>
                </div>
            </main>
        </>
    );
}
