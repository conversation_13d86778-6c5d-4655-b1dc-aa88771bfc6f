import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import {ViolationQueryParams} from "@/lib/types/types";
import DisplayPlaque from "@/components/common/DisplayPlaque";

type Props = {
    data: ViolationQueryParams
}

export function InquiryInfo({data}: Props) {
    const {left, right, middle, alphabet, isMotor} = data
    return (
        <div className='mt-5'>
            <ChoiceWrapper
                variant='info'
                className='mt-2'>

                <div className='w-full flex flex-col justify-center items-center gap-2 py-3'>
                    <div className='w-full'>
                        <DisplayPlaque left={left} right={right} middle={middle} alphabet={alphabet}
                                       isMotor={isMotor === 'true'}/>
                    </div>
                    <div className='w-full flex justify-center items-center'>
                        <span className='text-sm text-destructive'>{data.message}</span>
                    </div>
                </div>
            </ChoiceWrapper>
        </div>
    );
}
