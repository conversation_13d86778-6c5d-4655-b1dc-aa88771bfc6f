'use client'

import {AlertTriangle} from "lucide-react";
import {useEffect} from "react";
import { useRouter } from 'nextjs-toploader/app';
import CustomButton from "@/components/UI/CustomButton";
import Card from "@/components/common/Card";
import {logOut} from "@/actions/auth.action";
import {LOGIN_PATH} from "@/lib/routes";

export default function Error500() {
    const router = useRouter();

    useEffect(() => {
        // Redirect user to this page on error (this can be handled globally in _app.js or middleware)
    }, []);

    async function onLogoutClick() {
        await logOut();
        router.replace(LOGIN_PATH);
    }

    return (
        <div className="flex px-2 flex-col items-center justify-center h-screen bg-gray-100 text-center">
            <Card className="max-w-[480px] !p-5">
                <div className='flex flex-col justify-center items-center'>
                    <div
                        className='w-full flex items-center justify-start pr-10 gap-20 bg-gradient-to-b rounded-t-2xl py-7 from-red-300 to-transparent'>
                        <AlertTriangle size={64} className="text-red-500 my-4"/>
                        <h1 className="text-2xl font-bold text-gray-500 mb-2">خطای سرور</h1>
                    </div>
                    <p className="text-sm md:text-base text-gray-600 my-14">
                        مشکلی در سرور پیش آمده است. لطفاً بعداً دوباره امتحان کنید.
                    </p>
                    <CustomButton className='py-4' onClick={() => router.back()}>
                        تلاش مجدد
                    </CustomButton>
                    <CustomButton onClick={onLogoutClick}
                                  className='mt-4 py-4 border bg-white text-primary border-primary'>
                        خروج از سایت
                    </CustomButton>
                </div>
            </Card>
        </div>
    );
}
