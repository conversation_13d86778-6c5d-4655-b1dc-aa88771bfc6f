import {cn} from "@/lib/utils";
import {<PERSON><PERSON><PERSON><PERSON>oa<PERSON>} from "react-spinners";

type LoadingProps = {
    className?: string;
};

export default function Loading({className}: LoadingProps) {
    return (
        <div className={cn("flex flex-col justify-center items-center h-screen w-screen", className)}>
            <BounceLoader
                color="#d5af0e"
                loading
            />
            <p className='text-xs text-neutral-500 mt-4'>لطفا منتظر بمانید</p>
        </div>
    );
}