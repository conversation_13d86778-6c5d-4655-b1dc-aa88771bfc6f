"use client";
import React, { useState } from "react";
import AccordionItem from "./AccordionItem";
import { FAQProps } from "../InquiryStaticComponents/Faq";



const CustomAccordion: React.FC<FAQProps> = ({ faqs }) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleAccordionClick = (index: number) => {
    setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
  };


  console.log(faqs);
  return (

    <div className="md:container w-[95%] mx-auto">
      {faqs.map((item, index) => (
        <AccordionItem
          title={item.question}
          index={index}
          isOpen={openIndex === index}
          key={index}
          onClick={handleAccordionClick}
        >



          <p
            className="mb-2 text-base text-justify"
            dangerouslySetInnerHTML={{ __html: item.answer.replace(/\n/g, '<br />') }}
          ></p>

        </AccordionItem>
      ))}
    </div>
  );
};

export default CustomAccordion;
