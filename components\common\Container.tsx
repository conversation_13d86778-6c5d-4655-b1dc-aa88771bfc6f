import {ReactNode} from "react";
import {cn} from "@/lib/utils";
import Image from 'next/image';
import patternSvg from '@/public/assets/images/pattern.webp'; // Move SVG to public folder

type Props = {
    children: ReactNode
    center?: boolean,
    className?: string
    backgroundHeight?: number
}

export default function Container({children, center, className, backgroundHeight}: Props) {
    const bgHeight = backgroundHeight ? backgroundHeight : center ? '' : ''
    return (
        <div className={cn('relative z-0', {
            "min-h-screen": center,
        })}>
             <div className=" inset-0 z-0">
                <Image
                    src={patternSvg}
                    alt="Background pattern"
                    fill
                    priority
                    sizes="100vh"
                    // className="object-cover"
                />
            </div>
            <div className='z-10 relative'>
                <div
                    className={cn('w-full py-32 bg-gray-100 z-[-10] absolute left-0 bottom-0  broken-div')}></div>
                <div className={cn('w-full  px-2 md:px-0 flex justify-center', className, {
                    "items-center !min-h-screen": center,
                    "items-start pt-4 md:pt-8": !center,
                })}>
                    {children}
                </div>
            </div>

        </div>
    );
}
