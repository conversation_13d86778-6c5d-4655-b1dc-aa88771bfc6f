'use client';

import {  useState } from 'react';
import { Pencil, Trash } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog';
import EditAddressForm from './EditAddress'; // (your separated EditAddressForm)
import { UserAddress } from '@/lib/types/types';
import EditAddressModal from '../shop/address/EditAddressModal';

// type AddressItemType = {
//   id: number;
//   address: string;
//   details?: string;
//   title?: string;
//   phone?: string;
// };

// const addressesData: AddressItemType[] = [
//   { id: 1, address: 'اصفهان، جلفا، بوستان سعدی', title: 'منزل' },
//   { id: 2, address: 'سعادت آباد، سعادت آباد (آزادگان)، منصور رفیعیان', title: 'شرک<PERSON>', details: 'تماس بگیرید', phone: '09103528525' },
//   { id: 3, address: 'بوشهر، باغ زهرا سامان، بلوار ۱۶', title: 'خانه دوم' },
//   { id: 4, address: 'اصفهان، جلفا، بوستان سعدی', title: 'منزل' },
//   { id: 5, address: 'سعادت آباد، سعادت آباد (آزادگان)، منصور رفیعیان', title: 'شرکت', details: 'تماس بگیرید', phone: '09103528525' },
//   { id: 6, address: 'بوشهر، باغ زهرا سامان، بلوار ۱۶', title: 'خانه دوم' },
// ];

function getShortAddress(fullAddress: string) {
  const words = fullAddress.split('،').map(word => word.trim());
  return words.slice(0, 2).join('، ');
}

export default function AddressModal({addressList}: {addressList: UserAddress[]}) {
  console.warn(addressList);
  
  const [addresses, setAddresses] = useState<UserAddress[]>(addressList);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(addresses[0]?.id || null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'select' | 'edit'>('select');
  const [addressToEdit, setAddressToEdit] = useState<UserAddress | null>(null);

  function handleUpdateAddress(updatedAddress: UserAddress) {
    if (updatedAddress.id) {
      // Update existing address
      setAddresses(prev =>
        prev.map(addr => (addr.id === updatedAddress.id ? updatedAddress : addr))
      );
    } else {
      // Create new address
      // const newAddress = { ...updatedAddress, id: Date.now() };
      // setAddresses(prev => [...prev, newAddress]);
      // setSelectedAddressId(newAddress.id);
    }
    setModalMode('select');
  }


  return (
    <>
      <button onClick={() => setIsModalOpen(true)} className="text-primary">
        مکان من: {getShortAddress(addresses.find(a => a.id === selectedAddressId)?.name || '')}
      </button>

      <EditAddressModal
        addressToEdit={addressToEdit}
        setAddressToEdit={setAddressToEdit}
        handleUpdateAddress={handleUpdateAddress}
        setModalMode={setModalMode}
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        setIsModalOpen={setIsModalOpen}
        addresses={addresses}
        modalMode={modalMode}
        selectedAddressId={selectedAddressId}
        setSelectedAddressId={setSelectedAddressId}
        setAddresses={setAddresses}
      />
    </>
  );
}
