import CustomButton from "@/components/UI/CustomButton";
import { ReactNode } from "react";
import { ClipLoader } from 'react-spinners'
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import SuccessStatusIcon from "@/components/common/svg/SuccessStatusIcon";
import { cn } from "@/lib/utils";

type Props = {
    status: 'Failed' | 'Success';
    message: string;
    hideMessage?: boolean,
    children?: ReactNode;
    hideHeader?: boolean;
    title: string;
    actionButtonText?: string;
    actionButtonLink?: string;
    buttonEnabled?: boolean;
    description?: any;
    loading?: boolean
};


export default function PaymentResultCard({
    status,
    children,
    message,
    title,
    actionButtonText,
    actionButtonLink,
    description,
    hideHeader,
    loading,
    hideMessage = false,
    buttonEnabled = true
}: Props) {
    const isSuccess = status === "Success";

    return (
        <div className='w-full flex gap-y-5 flex-col justify-center'>
            <>
                {
                    loading ? (<div className='w-full flex flex-col gap-1 h-[200px] justify-center items-center'>
                        <ClipLoader />
                        <span className='text-xs mt-2 text-neutral-500'>لطفا منتظر باشید</span>
                    </div>) : (
                        <>
                            {!hideHeader && <div
                                className={cn('h-[150px] flex pr-10 items-center bg-gradient-to-b rounded-t-2xl  to-transparent text-sm font-semibold gap-14 text-center text-[#212121]', {
                                    "from-green-300": isSuccess,
                                    "from-red-300": !isSuccess,
                                })}>
                                <SuccessStatusIcon isSuccess={isSuccess} />
                                <div className="flex gap-3 flex-col"><h4
                                    className="text-xl text-[#3E3E3E]">{title}</h4><h5><span
                                        className="text-base text-neutral-500">{message}</span></h5>
                                </div>

                            </div>}

                            <div className='px-4 flex flex-col gap-5'>
                                {description && (
                                    <p className="text-sm text-center mt-7 text-gray-600">{description}</p>
                                )}

                                <div className='w-full'>{children}</div>

                                {buttonEnabled && (
                                    <div className='w-full flex flex-col items-center'>
                                        <CustomButton variants='black' className='py-4' href={actionButtonLink}>
                                            <span className='text-sm'>
                                                {actionButtonText}
                                            </span>
                                        </CustomButton>
                                        <CustomButton href="/"
                                            className='mt-2 py-4 border bg-white text-primary border-primary'>
                                            <span className='text-sm'>
                                                بازگشت به صفحه اصلی
                                            </span>
                                        </CustomButton>
                                    </div>
                                )}
                            </div>

                        </>
                    )
                }
            </>


        </div>
    );

}
