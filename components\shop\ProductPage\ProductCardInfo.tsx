'use client'

import { LayoutGrid } from 'lucide-react';
import { AttributeValue, ProductData, Variation } from '@/lib/types/product.types';
import SelectColor from './SelectColor';
import SelectSize from './SelectSize';
// import ProviderShopInfo from './ProviderShopInfo';


interface ProductCardInfoProps extends ProductData {
    onChange?: (color: AttributeValue | null) => void;
    onSizeChange?: (size: string | null) => void;
    selectedColor?: AttributeValue | null;
    selectedSize?: string | null;
    selectedVariant: Variation | null
}

const ProductCardInfo = ({
    details,
    attributes,
    categories,
    variations,
    description,
    onChange,
    onSizeChange,
    selectedColor,
    selectedSize,
    selectedVariant
}: ProductCardInfoProps) => {
    
    
    const handleColorChange = (color: AttributeValue | null) => {
        if (onChange) {
            onChange(color);
        }
    };

   
    const handleSizeChange = (size: string | null) => {
        
        if (onSizeChange) {
            onSizeChange(size);
        }
    };
    return (
        <div>
            <div className='flex flex-wrap product-details justify-between text-sm'>
                {details.map((detail) => (
                    <div key={detail.key}>
                        <h3>{detail.key}</h3>
                        <span>{detail.value}</span>
                    </div>
                ))}
            </div>

            <div className='max-md:mt-4'>
                <div>
                    <h3 className='my-2 max-md:mt-2 flex items-center gap-2'>
                        رنگ انتخابی شما
                        {/* {selectedColor && (
                            <span className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full text-sm">
                                <span
                                    className="inline-block w-3 h-3 rounded-full"
                                    style={{
                                        backgroundColor: selectedColor.extra_data?.hex || '#ccc'
                                    }}
                                ></span>
                                {selectedColor.value}
                            </span>
                        )} */}
                    </h3>
                    <SelectColor
                        attributes={attributes}
                        defaultValue={selectedColor}
                        onChange={handleColorChange}
                    />

                    <div className="mt-4">
                        <h3 className='my-2 max-md:mt-2 flex items-center gap-2'>
                            سایز انتخابی
                            {/* {selectedSize && (
                                <span className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full text-sm">
                                    {selectedSize}
                                </span>
                            )} */}
                        </h3>

                        <SelectSize
                            attributes={attributes}
                            variations={variations}
                            selectedColor={selectedColor}
                            defaultValue={selectedSize}
                            onChange={handleSizeChange}
                            disabled={!selectedColor}
                            selectedVariant={selectedVariant}
                        />
                    </div>
                </div>

                <div className='category text-[#9DA5B0] flex md:flex-col items-start gap-5 mb-5 text-sm max-md:mt-5'>
                    <span className='flex gap-2'><LayoutGrid /> دسته بندی</span>
                    <div className='flex justify-between gap-3 flex-wrap'>
                        {categories.map((category) => (
                            <span key={category} className='bg-[#F9FAFB] border-2 border-[#F1F1F6] px-2.5 py-1 rounded-3xl whitespace-nowrap'>
                                {category}
                            </span>
                        ))}
                    </div>
                </div>

                <div>
                    <p className='text-justify leading-8 text-sm'>
                        {description}
                    </p>
                </div>
            </div>
            {/* <ProviderShopInfo /> */}
        </div>
    )
}

export default ProductCardInfo