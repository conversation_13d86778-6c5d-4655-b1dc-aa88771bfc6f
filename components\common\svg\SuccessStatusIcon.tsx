import {CheckCircle, X} from "lucide-react";
import {cn} from "@/lib/utils";

type Props = {
    isSuccess?: boolean
}

export default function SuccessStatusIcon({isSuccess}: Props) {
    return (
        <div
            className={cn('h-[60px] w-[60px] border border-transparent flex justify-center items-center rounded-full', {
                "border-green-600 bg-green-600": isSuccess,
                "border-red-500 bg-red-500": !isSuccess
            })}>
            {
                isSuccess ? <CheckCircle color='white'/> : <X color='white'/>
            }
        </div>
    );
}
