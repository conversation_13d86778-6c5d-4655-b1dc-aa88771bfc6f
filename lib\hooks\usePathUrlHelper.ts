'use client'

import {useSearchParams} from 'next/navigation';
import {
    INQUIRY_RESULT_PATH,
    LOGIN_PATH,
    PAYMENT_PATH
} from "@/lib/routes";


type PathUrlOptions = {
    currentUrlQuery?: boolean
}


const usePathUrlHelper = () => {
    const searchParams = useSearchParams();

    const getQueryParams = <T extends Record<string, string>>(): T => {
        const params = Object.fromEntries(searchParams.entries()) as Record<string, string>;

        // Decode each key and value
        const decodedParams = Object.fromEntries(
            Object.entries(params).map(([key, value]) => [
                decodeURIComponent(key),
                decodeURIComponent(value)
            ])
        );

        return decodedParams as T;
    };

    const generateQueryParams = <T extends Record<string, string>>(params?: T, options: PathUrlOptions = {currentUrlQuery: true}) => {

        const currentParams = new URLSearchParams(options?.currentUrlQuery ? searchParams.toString() : '');

        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value) {
                    currentParams.set(encodeURIComponent(key), encodeURIComponent(value));
                }
            });
        }
        const queryString = currentParams.toString();
        return `${queryString ? '?' + queryString : ''}`;
    };

    const toPaymentUrl = <T extends Record<string, string>>(params?: T, options?: PathUrlOptions) => {
        const queryParams = generateQueryParams(params, options)
        return `${PAYMENT_PATH}${queryParams ? queryParams : ''}`
    };

    const toLoginUrl = <T extends Record<string, string>>(params?: T, options?: PathUrlOptions) => {
        const queryParams = generateQueryParams(params, options)
        return `${LOGIN_PATH}${queryParams ? queryParams : ''}`
    };

    // const toInquiryUrl = <T extends Record<string, string>>(params?: T, options?: PathUrlOptions) => {
    //     const isMotor = params!.isMotor === 'true';
    //     const queryParams = generateQueryParams(params, options)
    //     return `${isMotor ? MOTOR_INQUIRY_PATH : CAR_INQUIRY_PATH}${queryParams ? queryParams : ''}`
    //
    // };

    const toInquiryResultUrl = (params: {
        traceNumber: string,
        reInquiry: 'true' | 'false',
        isMotor: 'true' | 'false'
    }) => {
        const queryParams = generateQueryParams(params, {currentUrlQuery: false})
        return `${INQUIRY_RESULT_PATH}${queryParams ? queryParams : ''}`
    };

    return {getQueryParams, generateQueryParams, toLoginUrl, toPaymentUrl, toInquiryResultUrl};
};

export default usePathUrlHelper;

