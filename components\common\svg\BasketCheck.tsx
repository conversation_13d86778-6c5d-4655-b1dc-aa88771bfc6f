import React from "react"

interface BasketCheckProps {
    size?: string
    color?: string
    fill?: string
    stroke?: string
    className?: string
}
const BasketCheck: React.FC<BasketCheckProps> = ({color, size, fill, stroke, className}) => {
    return (
        <svg className={className || ""} color={color || ""} fill={fill || ""} stroke={stroke || ""} xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width={size || "50.961"} height={size || "50.961"} viewBox="0 0 50.961 50.961">
            <defs>
                <linearGradient id="linear-gradient" x1="0.83" y1="0.142" x2="-0.204" y2="0.207" gradientUnits="objectBoundingBox">
                    <stop offset="0" stopColor="#e4e7e9" />
                    <stop offset="1" stopColor="#fff" />
                </linearGradient>
            </defs>
            <g id="Huge-icon_ecommerce_outline_shopping_basket-check" data-name="Huge-icon/ecommerce/outline/shopping basket-check" opacity="0.9">
                <path id="Rectangle_784" data-name="Rectangle 784" d="M39.785,1.778v0Zm9.2,11.4,1.742.356Zm-47.011,0,1.742-.356Zm9.2-11.4v0ZM15.051,39.7V37.924h0Zm-9.2-7.566-1.742.356ZM35.91,39.7v1.778h0Zm9.2-7.566,1.742.356Zm-9.2,5.788H15.051v3.555H35.91ZM7.592,31.78,3.717,12.818.233,13.53,4.108,32.492ZM11.176,3.555h28.61V0H11.176Zm36.069,9.263L43.369,31.78l3.483.712L50.728,13.53ZM39.785,3.555a7.68,7.68,0,0,1,7.459,9.263l3.483.712A11.235,11.235,0,0,0,39.785,0ZM3.717,12.818a7.68,7.68,0,0,1,7.459-9.263V0A11.235,11.235,0,0,0,.233,13.53ZM15.051,37.924A7.638,7.638,0,0,1,7.592,31.78l-3.483.712a11.193,11.193,0,0,0,10.942,8.988ZM35.91,41.479a11.193,11.193,0,0,0,10.943-8.988l-3.483-.712a7.638,7.638,0,0,1-7.459,6.144Z" transform="translate(0 9.481)" fill="url(#linear-gradient)" />
                <path id="Vector_2973" data-name="Vector 2973" d="M44.442,3.555H46.22V0H44.442ZM1.778,0H0V3.555H1.778ZM44.442,0H1.778V3.555H44.442Z" transform="translate(2.37 18.962)" fill="url(#linear-gradient)" />
                <path id="Vector_1805" data-name="Vector 1805" d="M2.888,5.13A1.778,1.778,0,0,0,.667,7.906ZM17.337,2.948A1.778,1.778,0,1,0,14.661.607ZM5.932,9.842,4.822,11.23ZM.667,7.906,4.822,11.23,7.043,8.454,2.888,5.13Zm9.867,2.816,6.8-7.774L14.661.607l-6.8,7.774Zm-5.713.508a4.148,4.148,0,0,0,5.713-.508L7.859,8.381a.593.593,0,0,1-.816.073Z" transform="translate(16.592 28.443)" fill="url(#linear-gradient)" />
                <path id="Vector_1847" data-name="Vector 1847" d="M10.311,2.844A1.778,1.778,0,0,0,7.466.711ZM.356,10.192A1.778,1.778,0,1,0,3.2,12.325ZM7.466.711.356,10.192,3.2,12.325l7.111-9.481Z" transform="translate(9.481 0)" fill="url(#linear-gradient)" />
                <path id="Vector_1848" data-name="Vector 1848" d="M3.2.711A1.778,1.778,0,1,0,.356,2.844ZM7.466,12.325a1.778,1.778,0,0,0,2.844-2.133ZM.356,2.844l7.111,9.481,2.844-2.133L3.2.711Z" transform="translate(30.813 0)" fill="url(#linear-gradient)" />
            </g>
        </svg>

    )
}

export default BasketCheck