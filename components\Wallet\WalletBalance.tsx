'use client'

import {useAuth} from "@/lib/hooks/useAuth";
import {formatWithComma} from "@/utils/helpers";

export default function WalletBalance() {
    const {userData} = useAuth()
    return (
        <>
            <span
                className="text-xl text-blue-700">{userData ? formatWithComma(userData.balance.toString()) : '-'}</span>
            <span
                className="text-base mr-1 text-gray-500">تومان</span>
        </>
    );
}
