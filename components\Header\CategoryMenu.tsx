'use client'

import { useState, useRef, useEffect } from 'react'
import { LayoutGrid, ShieldCheck, Car, Bike, Lightbulb, ChevronLeft } from 'lucide-react'
import Image from 'next/image'
import MyBanner from "@/public/assets/images/banner.png"
const categories = [
  {
    label: 'لوازم یدکی خودرو',
    sub: {
      banner: '/banner-hub.jpg', 
      columns: [
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
        {
          title: 'دسته بندی شما در اینجا',
          items: ['دسته بندی شما در اینجا', 'دسته بندی شما در اینجا', 'دسته بندی شما در اینجا'],
        },
      ],
    },
  },
  {
    label: 'تایر و لاستیک',
    sub: null,
  },
  {
    label: 'لوازم بدنه خودرو',
    sub: {
      banner: '/banner-body.jpg',
      columns: [
        {
          title: 'دسته بندی شما در اینجا',
          items: ['گزینه اول', 'گزینه دوم', 'گزینه سوم'],
        },
        // ... more columns
      ],
    },
  },
  // Add more categories...
]

export default function CategoryMenu() {
  const [open, setOpen] = useState(false)
  const [activeIndex, setActiveIndex] = useState<number | null>(0)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setOpen(false)
        setActiveIndex(null)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative z-50" ref={menuRef}>
      <button
        onClick={() => setOpen(prev => !prev)}
        className="bg-primary text-white text-sm flex text-nowrap py-3 px-4 rounded-2xl gap-2 items-center"
      >
        <LayoutGrid className="w-5" />
        دسته بندی محصولات
      </button>

      {open && (
        <div className="absolute top-16 right-0 w-[50vw] max-w-7xl bg-white shadow-2xl rounded-2xl flex border overflow-hidden">
          {/* Right Sidebar - Main Categories */}
          <div className="w-64 bg-gray-50 border-l border-gray-200">
            <ul className="flex flex-col divide-y divide-gray-200 text-sm">
              {categories.map((cat, index) => (
                <li
                  key={index}
                  onMouseEnter={() => setActiveIndex(index)}
                  className={`flex items-center justify-between px-4 py-3 cursor-pointer hover:bg-white transition ${activeIndex === index ? 'bg-white font-semibold text-primary' : ''
                    }`}
                >
                  <span>{cat.label}</span>
                  {cat.sub && <span className="text-gray-400">&lsaquo;</span>}
                </li>
              ))}
            </ul>
          </div>

          {/* Left Content Area - Subcategories */}
          {/* Left Content Area - Subcategories */}
          {activeIndex !== null && categories[activeIndex]?.sub && (
            <div className="flex-1 p-6">
              <div className='flex items-center justify-between pb-2 mb-3 border-b'>
                <h3 className='text-base relative before:absolute before:bg-primary before:h-[1px] before:w-full before:-bottom-2'>
                  لوازم بدنه خودرو
                </h3>
                <span className='text-base flex items-center !font-extralight'>
                  مشاهده همه محصولات
                  <ChevronLeft className='w-5' />
                </span>
              </div>
              <div className="grid grid-cols-4 grid-rows-2 gap-6">
                {/* Banner spans 2 columns and 1 row */}
               

                {/* First row: two category columns */}
                {categories[activeIndex].sub!.columns.slice(0, 2).map((col, idx) => (
                  <div key={idx} className="row-span-1">
                    <div className="text-sm font-bold mb-3">{col.title}</div>
                    <ul className="space-y-2 text-xs text-gray-600">
                      {col.items.map((item, i) => (
                        <li key={i} className="hover:text-primary transition cursor-pointer">
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}

                {/* Second row: remaining category columns (span full row) */}
                {categories[activeIndex].sub!.columns.slice(2).map((col, idx) => (
                  <div key={idx} className="row-start-2">
                    <div className="text-sm font-bold mb-3">{col.title}</div>
                    <ul className="space-y-2 text-xs text-gray-600">
                      {col.items.map((item, i) => (
                        <li key={i} className="hover:text-primary transition cursor-pointer">
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
                 <div className="col-span-2 row-span-1 rounded-lg overflow-hidden">
                  <Image
                    src={MyBanner}
                    alt="banner"
                    width={600}
                    height={200}
                    className="w-full h-full object-cover rounded-xl"
                  />
                </div>
              </div>
            </div>
          )}

        </div>
      )}
    </div>
  )
}
