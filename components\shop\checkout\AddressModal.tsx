"use client"
import { use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useR<PERSON>, useMemo } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/UI/dialog"
import { Input } from "@/components/UI/input"
import { But<PERSON> } from "@/components/UI/button"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap, useMapEvents } from "react-leaflet"
import "leaflet/dist/leaflet.css"
import { DialogTitle } from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"
import { DialogClose } from "@radix-ui/react-dialog"
import { Label } from "@/components/UI/label"
import L from 'leaflet'
import toast from "react-hot-toast"
import { createUserAddressAction } from "@/actions/userAddress.action"

const defaultPosition: [number, number] = [35.6892, 51.3890] // Tehran center

/**
 * Address form data structure matching API requirements
 */
export interface AddressFormData {
    name: string;
    receiver_name: string;
    receiver_phone: string;
    is_recipient_self: boolean;
    province: string;
    city: string;
    zip_code: string;
    address: string;
    latitude: number;
    longitude: number;
}

/**
 * UI state for managing loading states and form interactions
 */
interface UIState {
    isReverseGeocodingInProgress: boolean;
    isAddressChangeInProgress: boolean;
    position: [number, number];
}

/**
 * Combined state for the address modal
 */
interface AddressModalState {
    formData: AddressFormData;
    uiState: UIState;
}

/**
 * Action types for the reducer
 */
type AddressModalAction =
    | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: string | number | boolean }
    | { type: 'UPDATE_COORDINATES'; latitude: number; longitude: number }
    | { type: 'UPDATE_POSITION'; position: [number, number] }
    | { type: 'SET_REVERSE_GEOCODING'; isInProgress: boolean }
    | { type: 'SET_ADDRESS_CHANGE'; isInProgress: boolean }
    | { type: 'UPDATE_ADDRESS_FROM_GEOCODING'; province: string; city: string; address: string }
    | { type: 'RESET_FORM' };

/**
 * Initial state for the address modal
 */
const initialState: AddressModalState = {
    formData: {
        name: "",
        receiver_name: "",
        receiver_phone: "",
        is_recipient_self: true,
        province: "",
        city: "",
        zip_code: "",
        address: "",
        latitude: defaultPosition[0],
        longitude: defaultPosition[1],
    },
    uiState: {
        isReverseGeocodingInProgress: false,
        isAddressChangeInProgress: false,
        position: defaultPosition,
    }
};

/**
 * Reducer function to manage address modal state
 */
function addressModalReducer(state: AddressModalState, action: AddressModalAction): AddressModalState {
    switch (action.type) {
        case 'UPDATE_FIELD':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    [action.field]: action.value,
                },
            };

        case 'UPDATE_COORDINATES':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    latitude: action.latitude,
                    longitude: action.longitude,
                },
                uiState: {
                    ...state.uiState,
                    position: [action.latitude, action.longitude],
                },
            };

        case 'UPDATE_POSITION':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    latitude: action.position[0],
                    longitude: action.position[1],
                },
                uiState: {
                    ...state.uiState,
                    position: action.position,
                },
            };

        case 'SET_REVERSE_GEOCODING':
            return {
                ...state,
                uiState: {
                    ...state.uiState,
                    isReverseGeocodingInProgress: action.isInProgress,
                },
            };

        case 'SET_ADDRESS_CHANGE':
            return {
                ...state,
                uiState: {
                    ...state.uiState,
                    isAddressChangeInProgress: action.isInProgress,
                },
            };

        case 'UPDATE_ADDRESS_FROM_GEOCODING':
            return {
                ...state,
                formData: {
                    ...state.formData,
                    province: action.province,
                    city: action.city,
                    address: action.address,
                },
            };

        case 'RESET_FORM':
            return initialState;

        default:
            return state;
    }
}

// Custom component to update map view when position changes
const ChangeView = ({ center }: { center: [number, number] }) => {
    const map = useMap();
    useEffect(() => {
        map.setView(center, 16);
    }, [center, map]);
    return null;
};

// Custom component to handle map clicks and marker dragging
const LocationMarker = ({
    position,
    onPositionChange,
    onLocationSelected
}: {
    position: [number, number];
    onPositionChange: (pos: [number, number]) => void;
    onLocationSelected: (lat: number, lng: number) => void;
}) => {
    const markerRef = useRef<L.Marker>(null);

    // Handle map clicks to set marker position
    useMapEvents({
        click(e) {
            const { lat, lng } = e.latlng;
            onPositionChange([lat, lng]);
            onLocationSelected(lat, lng);
        },
    });

    // Handle marker drag end
    const eventHandlers = useMemo(
        () => ({
            dragend() {
                const marker = markerRef.current;
                if (marker != null) {
                    const newPosition = marker.getLatLng();
                    onPositionChange([newPosition.lat, newPosition.lng]);
                    onLocationSelected(newPosition.lat, newPosition.lng);
                }
            },
        }),
        [onLocationSelected, onPositionChange]
    );

    return (
        <Marker
            draggable={true}
            eventHandlers={eventHandlers}
            position={position}
            ref={markerRef}
        >
            <Popup>
                <div className="text-center">
                    <p>این مکان انتخاب شده است</p>
                    <p className="text-xs text-gray-500">
                        برای تغییر مکان، نشانگر را بکشید یا روی نقطه دیگری از نقشه کلیک کنید
                    </p>
                </div>
            </Popup>
        </Marker>
    );
}

export default function AddressModal({
    open,
    onClose,
    onAddressCreated
}: {
    open: boolean;
    onClose: () => void;
    onAddressCreated?: () => void;
}) {
    const [state, dispatch] = useReducer(addressModalReducer, initialState);

    // Destructure state for easier access
    const { formData, uiState } = state;
    const {
        name,
        receiver_name,
        receiver_phone,
        is_recipient_self,
        province,
        city,
        zip_code,
        address
    } = formData;

    const {
        isReverseGeocodingInProgress,
        position
    } = uiState;

    /**
     * Helper function to update form fields
     */
    const updateField = useCallback((field: keyof AddressFormData, value: string | number | boolean) => {
        dispatch({ type: 'UPDATE_FIELD', field, value });
    }, []);

    // Fix Leaflet marker icon issue in Next.js
    useEffect(() => {
        // Only run on client side
        if (typeof window !== 'undefined') {
            // @ts-expect-error - Known issue with Leaflet in Next.js
            delete L.Icon.Default.prototype._getIconUrl;

            L.Icon.Default.mergeOptions({
                iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
                iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
                shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
            });
        }
    }, []);

    // Forward geocoding: Convert address text to coordinates
    const handleAddressChange = useCallback(async () => {
        // Skip if we're currently processing a reverse geocoding request
        if (isReverseGeocodingInProgress || !province || !city) return;

        dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: true });
        try {
            const query = `${province} ${city} ${address}`;
            const res = await fetch(`https://api.neshan.org/v5/search?term=${encodeURIComponent(query)}&lat=35.6892&lng=51.3890`, {
                headers: {
                    "Api-Key": process.env.NEXT_PUBLIC_NESHAN_API_KEY || "",
                },
            });
            const data = await res.json();
            if (data.topics && data.topics.length > 0) {
                const { location } = data.topics[0];
                dispatch({
                    type: 'UPDATE_POSITION',
                    position: [parseFloat(location.y), parseFloat(location.x)]
                });
            }
        } catch (error) {
            console.error("Error in forward geocoding:", error);
        } finally {
            dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: false });
        }
    }, [province, city, address, isReverseGeocodingInProgress]);

    // Reverse geocoding: Convert coordinates to address
    const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
        dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: true });
        try {
            // Use OpenStreetMap's Nominatim service for reverse geocoding
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
                {
                    headers: {
                        "Accept-Language": "fa", // Request Persian results
                        "User-Agent": "KhodRox Address Picker" // Required by Nominatim's terms
                    }
                }
            );

            const data = await response.json();

            if (data && data.address) {
                // Extract address components
                const {
                    state,
                    city,
                    town,
                    village,
                    suburb,
                    neighbourhood,
                    road,
                    house_number
                } = data.address;

                // Determine province and city
                const detectedProvince = state || "";
                let detectedCity = "";
                if (city) {
                    detectedCity = city;
                } else if (town) {
                    detectedCity = town;
                } else if (village) {
                    detectedCity = village;
                }

                // Build detailed address
                let detailedAddress = "";
                if (suburb) detailedAddress += suburb + " ";
                if (neighbourhood) detailedAddress += neighbourhood + " ";
                if (road) detailedAddress += road + " ";
                if (house_number) detailedAddress += "پلاک " + house_number;

                // Update all address fields at once
                dispatch({
                    type: 'UPDATE_ADDRESS_FROM_GEOCODING',
                    province: detectedProvince,
                    city: detectedCity,
                    address: detailedAddress.trim()
                });
            }
        } catch (error) {
            console.error("Error in reverse geocoding:", error);
        } finally {
            dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: false });
        }
    }, []);

    // Update map when address fields change
    useEffect(() => {
        if ((province || city || address) && !isReverseGeocodingInProgress) {
            handleAddressChange();
        }
    }, [province, city, address, handleAddressChange, isReverseGeocodingInProgress]);

    const handleCreateAddress = async () => {
        // Prepare the final data object matching API requirements
        const addressData: AddressFormData = {
            name: formData.name,
            receiver_name: formData.receiver_name,
            receiver_phone: formData.receiver_phone,
            is_recipient_self: formData.is_recipient_self,
            province: formData.province,
            city: formData.city,
            zip_code: formData.zip_code,
            address: formData.address,
            latitude: formData.latitude,
            longitude: formData.longitude
        };



        const createAddressResponse = await createUserAddressAction(addressData);
        console.log(createAddressResponse);


        dispatch({ type: 'RESET_FORM' });
        if (createAddressResponse.success) {
            toast.success("ادرس جدید با موفقیت ایجاد شد")
            // Notify parent component to refresh address list
            onAddressCreated?.();
        } else {
            toast.error(createAddressResponse.message)
        }
        onClose();
    }

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="md:w-full max-md:max-w-[93%] max-w-4xl bg-white p-0 z-50 rounded-lg shadow-lg [&>button]:hidden max-h-[85vh] top-[50.5vh] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar]:absolute [&::-webkit-scrollbar]:right-0" dir="rtl" >
                {/* Modal Header */}
                <div className="flex items-center justify-between border-b bg-white p-4 max-md:p-3 rounded-t-lg flex-shrink-0">
                    <DialogTitle className="text-xl font-semibold max-md:text-lg">افزودن آدرس جدید</DialogTitle>
                    <div className="bg-gradient-to-t from-gray-100 to-transparent py-2.5 px-2 max-md:py-2 max-md:px-1.5 rounded-b-full">
                        <DialogClose asChild>
                            <button onClick={onClose} className="text-gray-600 border-2 border-gray-400 rounded-full p-1 hover:text-gray-400 focus:outline-none">
                                <XIcon className="h-6 w-6" />
                            </button>
                        </DialogClose>
                    </div>
                </div>

                <div className="flex-1 overflow-hidden">
                    {/* Form Section */}
                    <div className="md:grid md:grid-cols-2 max-md:flex max-md:flex-col h-full">
                        <div className="flex flex-col gap-4 px-6 py-4 bg-white overflow-y-auto max-h-[calc(85vh-120px)] max-md:max-h-[40vh]
                            [&::-webkit-scrollbar]:w-2
                            [&::-webkit-scrollbar-track]:bg-gray-100
                            [&::-webkit-scrollbar-thumb]:bg-gray-300
                            [&::-webkit-scrollbar-thumb]:rounded-full
                            [&::-webkit-scrollbar]:absolute
                            [&::-webkit-scrollbar]:right-0"
                            dir="rtl"
                            style={{ direction: 'ltr', textAlign: 'right' }}>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">نام آدرس</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="مثال: خانه، محل کار"
                                    value={name}
                                    onChange={(e) => updateField('name', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">استان</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="استان خود را انتخاب کنید"
                                    value={province}
                                    onChange={(e) => updateField('province', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">شهر</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="شهر خود را وارد کنید"
                                    value={city}
                                    onChange={(e) => updateField('city', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">کد پستی</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="کد پستی ده رقمی"
                                    value={zip_code}
                                    onChange={(e) => updateField('zip_code', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">آدرس کامل</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="مثال: کوچه شهید بهشتی، پلاک 41"
                                    value={address}
                                    onChange={(e) => updateField('address', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">نام گیرنده</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="نام گیرنده را وارد کنید"
                                    value={receiver_name}
                                    onChange={(e) => updateField('receiver_name', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label className="text-base max-md:text-sm p-1 text-gray-500">شماره تماس گیرنده</Label>
                                <Input
                                    className="bg-[#F9FAFB] py-7 max-md:py-5 border-[#EFEFEF] rounded-2xl"
                                    placeholder="09xxxxxxxxx"
                                    value={receiver_phone}
                                    onChange={(e) => updateField('receiver_phone', e.target.value)}
                                />
                            </div>
                            <div className="flex items-center gap-3">
                                <input
                                    type="checkbox"
                                    id="is_recipient_self"
                                    checked={is_recipient_self}
                                    onChange={(e) => updateField('is_recipient_self', e.target.checked)}
                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <Label htmlFor="is_recipient_self" className="text-sm text-gray-700">
                                    خودم گیرنده هستم
                                </Label>
                            </div>

                            {/* Submit Button - Positioned at bottom right of inputs */}
                            <div className="mt-6 px-6 pb-4">
                                <Button
                                    onClick={handleCreateAddress}
                                    disabled={!province || !city || !address || !receiver_name || isReverseGeocodingInProgress}
                                    className="bg-primary px-8 py-3 text-white hover:bg-blue-400 rounded-xl w-full disabled:bg-gray-400 disabled:cursor-not-allowed"
                                >
                                    {isReverseGeocodingInProgress ? 'در حال پردازش...' : 'ثبت آدرس جدید'}
                                </Button>
                            </div>

                        </div>
                        <div className="flex flex-col p-4 max-md:px-6">
                            {/* Map Container */}
                            <div className="w-full h-[300px] max-md:h-[200px] relative rounded-lg overflow-hidden border border-gray-200">
                                {/* Map instructions */}
                                <div className="absolute top-2 right-2 z-[1000] bg-white p-2 rounded-lg shadow-md text-xs max-w-[180px]">
                                    <p className="font-bold mb-1">راهنمای نقشه:</p>
                                    <p>برای انتخاب مکان، روی نقشه کلیک کنید یا نشانگر را بکشید.</p>
                                </div>

                                <MapContainer
                                    center={position}
                                    zoom={13}
                                    scrollWheelZoom={true}
                                    style={{ height: '100%', width: '100%' }}
                                >
                                    <TileLayer
                                        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                                    />
                                    <ChangeView center={position} />
                                    <LocationMarker
                                        position={position}
                                        onPositionChange={(pos) => dispatch({ type: 'UPDATE_POSITION', position: pos })}
                                        onLocationSelected={handleLocationSelected}
                                    />
                                </MapContainer>

                                {isReverseGeocodingInProgress && (
                                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                                        <div className="bg-white p-3 rounded-lg">
                                            <p>در حال دریافت اطلاعات آدرس...</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
