import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { NotificationService } from './NotificationService.js';

export class FirebaseNotificationService extends NotificationService {
  constructor(config, vapidKey) {
    super();
    this.config = config;
    this.vapidKey = vapidKey;
    this.messaging = null;
  }

  async init() {
    const app = initializeApp(this.config);
    this.messaging = getMessaging(app);
    await Notification.requestPermission();
  }

  async getToken() {
    if (!this.messaging) throw new Error('Messaging not initialized');
    return await getToken(this.messaging, { vapidKey: this.vapidKey });
  }

  onForegroundMessage(callback) {
    if (!this.messaging) throw new Error('Messaging not initialized');
    onMessage(this.messaging, callback);
  }
}
