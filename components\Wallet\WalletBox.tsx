import Image from "next/image"
import RoundedArrow from "../common/svg/RoundedArrow"
import WalletImage from "@/public/assets/images/wallet.png"
import PaymentBox from "./PaymentBox"
import WalletBalance from "@/components/Wallet/WalletBalance";

const WalletBox = () => {
    return (
        <div className="max-w-[500px] w-[90%] mx-auto">
            <div className="text-center flex items-center py-8 md:py-12 justify-center gap-6">
                <div className="w-fit text-right">
                    <h1 className="text-3xl max-md:text-xl mb-2">کیف پول من</h1>
                    <span className="max-md:text-base">اعتبار خودتان را افزایش دهید</span>

                </div>
                <RoundedArrow/>
            </div>

            <div className="wallet mx-auto p-4 bg-white rounded-3xl md:p-4  shadow-lg">
                <div
                    className="wallet-header flex items-center gap-16 bg-gradient-to-b from-slate-200 to-white rounded-t-xl p-4">
                    <div className="">
                        <Image src={WalletImage} alt="wallet-image"/>
                    </div>
                    <div className="flex gap-3 flex-col">
                        <h4 className="text-xl">موجودی کیف پول من</h4>
                        <h5>
                            <WalletBalance/>
                        </h5>
                    </div>
                </div>

                <PaymentBox/>

            </div>
        </div>
    )
}

export default WalletBox