import {ResponseResult} from "../types/action-types";
import {ApiService} from "@/lib/types/action-types";
import fetchApi from "@/lib/fetch-api";


class inquiryService {

    inquireVehicle<T>(arg: ApiService): Promise<ResponseResult<T>> {
        const {type, ...payload} = arg.payload;
        return fetchApi.post(`user/inquiry/${type}`, payload || {})
    }

    getInquiry<T>(traceNumber: string): Promise<ResponseResult<T>> {
        return fetchApi.get(`user/inquiry/result/${traceNumber}`, undefined, undefined, true)
    }

    getInquiryHistory<T>(): Promise<ResponseResult<T>> {
        return fetchApi.get('user/history')
    }

}

export default new inquiryService()
