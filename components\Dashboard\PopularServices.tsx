// import Image from 'next/image'
// import Link from 'next/link'
// import CarInquiry from "@/public/assets/images/car-inquiry.png"
// import MotorInquiry from "@/public/assets/images/motor-inquiry.png"
// import CameraHistory from "@/public/assets/images/camera-history.png"
// import FuelCart from "@/public/assets/images/fuel-cart.png"
// import NomreManfi from "@/public/assets/images/nomre-manfi.png"
// import PlateHistory from "@/public/assets/images/plate-history.png"
// import { CctvIcon, CircleMinus, IdCard } from 'lucide-react'
// import LicensePlate from '../common/svg/LicensePlateIcon'
import DashboardServiceItems from '../Services/DashboardServiceItems'

const PopularServices = () => {
    return (
        <div className='w-full text-center md:h-auto max-md:py-6 bg-white shadow-md rounded-3xl max-md:order-3 '>
            <div className='flex justify-center'>
                <h2 className='bg-gradient-to-r from-gray-100 to-transparent px-10 py-2 md:mt-5 text-center rounded-full'> خدمات پر طرفدار </h2>
            </div>
            {/* <div className='flex justify-between md:flex-wrap md:p-4 p-2 md:gap-x-8 md:gap-y-5 mt-2'>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-yellow text-white'>
                        <svg className='md:w-12 md:h-12 w-8 h-8' xmlns="http://www.w3.org/2000/svg" width="18.864" height="16.301" viewBox="0 0 18.864 16.301">
                            <g id="Group_90659" data-name="Group 90659" transform="translate(16752.527 587.061)">
                                <g id="forbidden-sign-svgrepo-com" transform="translate(-16752.527 -587.061)">
                                    <path id="Path_196438" data-name="Path 196438" d="M8.28,7.93A4.383,4.383,0,0,0,3.218,4.467a4,4,0,0,0-2.8,2.309A1.216,1.216,0,0,0,.19,7.4,4.706,4.706,0,0,0,.056,9.449,4.083,4.083,0,0,0,4.31,12.619a.182.182,0,0,0,.1,0,5.038,5.038,0,0,0,.515-.044.136.136,0,0,0,.089-.062A5.169,5.169,0,0,0,7.6,11.03,3.58,3.58,0,0,0,8.28,7.93ZM1.824,9.7a3.216,3.216,0,0,1-.187-1.332A2.509,2.509,0,0,1,4.373,6.047a2.339,2.339,0,0,1,.7.133c-.124.169-.249.364-.346.48-.435.453-.906.9-1.368,1.341-.409.4-.817.8-1.19,1.226A2.807,2.807,0,0,0,1.824,9.7Zm2.14,1.545a.11.11,0,0,0-.071.036A2.1,2.1,0,0,1,2.952,11c.133-.107.249-.266.346-.355.426-.426.835-.87,1.252-1.314.373-.391.755-.755,1.155-1.11a4.651,4.651,0,0,0,.879-.853,2.6,2.6,0,0,1,.382,1.288C6.992,10.266,5.305,11.056,3.964,11.243Z" transform="translate(0 -4.383)" fill="#fff" />
                                </g>
                                <path id="car-crash-svgrepo-com_1_" data-name="car-crash-svgrepo-com(1)" d="M28.514,14.977,28.132,16.4a1.926,1.926,0,0,1-.539.9,2.056,2.056,0,0,1-.269.215l-.43,1.605a.983.983,0,0,1-1.2.7l-.95-.255a.984.984,0,0,1-.7-1.2l.255-.95-7.6-2.037-.255.95a.984.984,0,0,1-1.2.7l-.95-.255a.983.983,0,0,1-.7-1.2l.43-1.605a2.047,2.047,0,0,1-.126-.321,1.926,1.926,0,0,1-.016-1.051l.382-1.425A1.953,1.953,0,0,1,15.4,9.86a1.978,1.978,0,0,1,.568-.145l.557-.745L17.51,7.65a3.462,3.462,0,0,1,1.166-.994,3.375,3.375,0,0,1,.463-.2,3.412,3.412,0,0,1,.991-.186,3.47,3.47,0,0,1,1.028.117L24.928,7.4a3.464,3.464,0,0,1,.948.413,3.425,3.425,0,0,1,1.579,2.5l.2,1.638.11.924a1.982,1.982,0,0,1,.7.92,1.941,1.941,0,0,1,.054,1.18ZM16.764,11.319a.98.98,0,1,0-.508,1.894c.57.153,1.4.477,1.552-.092A1.892,1.892,0,0,0,16.764,11.319Zm8.933.866-.2-1.638A1.475,1.475,0,0,0,24.418,9.3l-3.771-1.01a1.476,1.476,0,0,0-1.563.541L18.1,10.149l5.287,1.416Zm.568,1.68A1.892,1.892,0,0,0,24.46,14.9c-.152.568.728.7,1.3.855a.98.98,0,1,0,.507-1.894Z" transform="translate(-16762.244 -590.613)" fill='#fff' />
                            </g>
                        </svg>

                    </div>
                </Link>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-[#32b3fe] text-white'>
                        <svg xmlns="http://www.w3.org/2000/svg" className='md:w-12 md:h-12 w-8 h-8' viewBox="0 0 17.833 15.91">

                            <g id="Group_90838" data-name="Group 90838" transform="translate(-621.979 -706.044)">
                                <path id="motorcycle-1-svgrepo-com" className="cls-1" d="M11.02,8a3.063,3.063,0,0,0-2.038.813.362.362,0,0,0,.512.512,2.1,2.1,0,0,1,1.3-.579,2.247,2.247,0,0,1,.189.5.382.382,0,0,0,.022.078,1.276,1.276,0,0,1-.278.6.606.606,0,0,1-.423.212c-2.174,0-2.6,1.036-2.817,1.57a.481.481,0,0,0-.067.212,3.35,3.35,0,0,1-2.138-.535c-.5-.855-.894-.891-2.461-.891-2.458,0-2.815,1.217-2.851,1.359a.342.342,0,0,0,.067.312,1.874,1.874,0,0,0,1.325.468h.178a4.04,4.04,0,0,1,1.6-.356,3.908,3.908,0,0,1,3.92,3.92,3.554,3.554,0,0,1-.111.858,6.874,6.874,0,0,0,1.682.212H8.7a3.187,3.187,0,0,0,1.96-.568.692.692,0,0,0,.212-.535,3.293,3.293,0,0,1,1.258-3.452c.142.324.287.65.445.969a3.143,3.143,0,0,0-1.2,2.483,3.207,3.207,0,1,0,3.207-3.174,3.069,3.069,0,0,0-1.392.323c.214.427.464.855.713,1.247a2.109,2.109,0,0,1,.679-.145A1.782,1.782,0,1,1,12.8,16.2a1.724,1.724,0,0,1,.5-1.247,11.1,11.1,0,0,1-.7-1.258,3.468,3.468,0,0,1,.59-.379,14.17,14.17,0,0,1-.858-1.993.382.382,0,0,0-.045-.089c-.287-.94-.564-2.1-.59-2.127a.392.392,0,0,0-.022-.056,3.577,3.577,0,0,0-.345-.857A.357.357,0,0,0,11.02,8ZM13.3,14.949A11.309,11.309,0,0,0,14.695,16.8a.325.325,0,0,0,.49,0,.335.335,0,0,0,0-.49,12.444,12.444,0,0,1-1.314-1.748A2.015,2.015,0,0,0,13.3,14.949Zm.067-5.524a3,3,0,0,0-.813.111c.214.784.49,1.808.668,2.272,0,.036.045.075.045.111h.1a1.428,1.428,0,0,0,.969-.29,1.185,1.185,0,0,0,.245-.891v-.134a1.185,1.185,0,0,0-.245-.891A1.428,1.428,0,0,0,13.37,9.425ZM3.18,12.989a3.207,3.207,0,1,0,3.174,3.564H4.929A1.8,1.8,0,0,1,3.18,17.978a1.782,1.782,0,0,1,0-3.564A1.8,1.8,0,0,1,4.929,15.84H6.354A3.2,3.2,0,0,0,3.18,12.989ZM4.929,15.84H4.194a1.049,1.049,0,0,0-1.013-.713,1.069,1.069,0,1,0,0,2.138,1.049,1.049,0,0,0,1.013-.713h.735a1.918,1.918,0,0,0,0-.713Z" transform="translate(622.021 702.551)" fill='#fff' />
                                <g id="forbidden-sign-svgrepo-com" transform="translate(623.55 706.5) rotate(-3)">
                                    <path id="Path_196438" data-name="Path 196438" className="cls-1" d="M8.608,3.687A4.557,4.557,0,0,0,3.345.087a4.154,4.154,0,0,0-2.908,2.4,1.265,1.265,0,0,0-.24.646A4.892,4.892,0,0,0,.059,5.266a4.245,4.245,0,0,0,4.422,3.3.189.189,0,0,0,.1,0,5.238,5.238,0,0,0,.535-.046.141.141,0,0,0,.092-.065A5.374,5.374,0,0,0,7.9,6.91,3.722,3.722,0,0,0,8.608,3.687ZM1.9,5.525A3.343,3.343,0,0,1,1.7,4.14,2.609,2.609,0,0,1,4.546,1.73a2.431,2.431,0,0,1,.729.138c-.129.175-.259.379-.36.5-.452.471-.942.932-1.422,1.394-.425.415-.849.831-1.237,1.274A2.918,2.918,0,0,0,1.9,5.525ZM4.121,7.131a.114.114,0,0,0-.074.037,2.182,2.182,0,0,1-.979-.286c.138-.111.259-.277.36-.369.443-.443.868-.9,1.3-1.366.388-.406.785-.785,1.2-1.154a4.835,4.835,0,0,0,.914-.886,2.7,2.7,0,0,1,.4,1.339C7.269,6.116,5.515,6.937,4.121,7.131Z" transform="translate(0 0)" fill='#fff' />
                                </g>
                            </g>
                        </svg>
                    </div>
                </Link>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-[#b373dd] text-white'>
                        <CctvIcon className='md:w-12 md:h-12 w-8 h-8' />
                    </div>
                </Link>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-[#329bfe] text-white'>
                        <IdCard className='md:w-12 md:h-12 w-8 h-8' />
                    </div>
                </Link>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-[#FF6D4A] text-white'>
                        <CircleMinus className='md:w-12 md:h-12 w-8 h-8' />
                    </div>
                </Link>
                <Link href="">
                    <div className='md:w-16 w-12 h-12 md:h-16 align-middle text-center flex justify-center items-center rounded-full bg-[#7482d1] text-white'>
                        <LicensePlate className='md:w-12 md:h-12 w-8 h-8' fill="#fff" />

                    </div>
                </Link>
            </div> */}
            <div>
                <DashboardServiceItems />
            </div>
        </div>
    )
}

export default PopularServices