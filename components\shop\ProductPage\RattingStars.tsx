import { Star } from "lucide-react";

const RatingStars = ({ rate }: { rate: number }) => {
  // محدود کردن مقدار rate بین 0 تا 5
  const clampedRate = Math.min(Math.max(0, rate), 5);
  // تعداد ستاره‌های پر
  const fullStars = Math.floor(clampedRate);
  // آیا ستاره نیمه نیاز داریم؟
  const hasHalfStar = clampedRate % 1 >= 0.5;
  // تعداد ستاره‌های خالی
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex gap-1 items-center h-full">
      {/* ستاره‌های پر */}
      {Array(fullStars).fill(0).map((_, i) => (
        <Star key={`full-${i}`} className="w-4 text-[#F7BC06]" fill="#F7BC06" />
      ))}
      
      {/* ستاره نیمه (اگر نیاز باشد) */}
      {hasHalfStar && (
        <div className="relative w-4 h-4">
          <Star className="absolute w-4 text-[#9DA5B0]" fill="#9DA5B0" />
          <div className="absolute w-2 overflow-hidden">
            <Star className="w-4 text-[#F7BC06]" fill="#F7BC06" />
          </div>
        </div>
      )}
      
      {/* ستاره‌های خالی */}
      {Array(emptyStars).fill(0).map((_, i) => (
        <Star key={`empty-${i}`} className="w-4 text-[#9DA5B0]" fill="#9DA5B0" />
      ))}
    </div>
  );
};

export default RatingStars