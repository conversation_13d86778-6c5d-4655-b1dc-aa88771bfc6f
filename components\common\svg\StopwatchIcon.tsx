import React from 'react';

const StopwatchIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="20.75"
    viewBox="0 0 17 20.75"
    fill="none"
    {...props}
  >
    <path
      d="M17 8.5A8.5 8.5 0 1 1 8.5 0 8.5 8.5 0 0 1 17 8.5Z"
      transform="translate(0 3.75)"
      fill="#1f84fb"
      opacity="0.4"
    />
    <path
      d="M3.75 1.5a10.258 10.258 0 0 0-2.8.386A.75.75 0 1 1 .546.442a11.825 11.825 0 0 1 6.408 0 .75.75 0 1 1-.408 1.443 10.258 10.258 0 0 0-2.796-.385Z"
      transform="translate(4.75)"
      fill="#1f84fb"
    />
    <g transform="translate(6.5 7)">
      <path
        d="M4 2A2 2 0 1 1 2 0a2 2 0 0 1 2 2Z"
        transform="translate(0 3.75)"
        fill="#1f84fb"
      />
      <path
        d="M.75 0A.75.75 0 0 1 1.5.75v4a.75.75 0 0 1-1.5 0v-4A.75.75 0 0 1 .75 0Z"
        transform="translate(1.25)"
        fill="#1f84fb"
      />
    </g>
  </svg>
);

export default StopwatchIcon;
