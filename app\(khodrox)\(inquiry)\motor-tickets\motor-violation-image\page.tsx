import ChildSchema from "@/components/common/ChildSchema";
import InquiryComponent from "@/components/inquiry/InquiryComponent";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("motor-tickets/motor-violation-image");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}

const isMotor: boolean = true
const withDetails: boolean | undefined = true


export default async function MotorViolationImagePage() {
    const data: PageContentResponse = await getPageContent("motor-tickets/motor-violation-image")
    const { schema, description, faqs, title } = data

    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="motor-violation-image"
                    schema={schema}
                />
            }

            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails} />
            {/* <MotorViolationImageAbout /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
