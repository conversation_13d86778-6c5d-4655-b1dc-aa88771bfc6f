'use server'

import {ActionResult, ArticleResponse} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import blogService from "@/lib/services/blog.service";

export async function getBlogPosts(): Promise<ActionResult<ArticleResponse[]>> {
    try {
        const responseResult = await blogService.getBlogPosts<ArticleResponse[]>()
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}
