import InquiryComponent from '@/components/inquiry/InquiryComponent'
// import Faq from '@/components/InquiryStaticComponents/Faq'
// import { getFaqs } from '@/lib/utils'
import { Metadata } from 'next';




export const metadata: Metadata = {
  title: "استعلام معاینه فنی خودرو با پلاک آنلاین- تاریخ اعتبار معاینه ماشین",
  description: "استعلام وضعیت معاینه فنی خودرو و پرداخت جریمه  ها به صورت آنلاین. خدمات سریع، دقیق و امن با همکاری مستقیم با پلیس راهور جمهوری اسلامی ایران.",
};


const isMotor: boolean = false
const withDetails: boolean | undefined = true
const CarCheckupPage = () => {
  // const relevantFAQs = getFaqs("carCheckup")
  return (
    <>
      <InquiryComponent title="استعلام و پرداخت جریمه معاینه فنی خودرو " isMotor={isMotor} withDetails={withDetails} />
      {/* <Faq faqs={relevantFAQs} className="mt-10 mb-10" > */}
        {/* <div className="my-7 p-1 text-lg max-md:p-3 text-justify leading-8 max-md:text-base">
          <p>
            در این بخش، به برخی از سوالات متداول در زمینه استعلام معاینه فنی خودرو و جریمه  ها پاسخ خواهیم داد:
          </p>
        </div> */}
      {/* </Faq> */}
    </>
  )
}

export default CarCheckupPage