import {ApiService, ResponseResult} from "@/lib/types/action-types";
import fetchApi from "@/lib/fetch-api";

class inquireService {

    paymentPost<T>(arg: ApiService): Promise<ResponseResult<T>> {
        return fetchApi.post(`user/payment`, arg.payload || {})
    }

    paymentVerificationPost<T>(arg: ApiService): Promise<ResponseResult<T>> {
        return fetchApi.post(`user/payment/verfication`, arg.payload || {})
    }


}

export default new inquireService()