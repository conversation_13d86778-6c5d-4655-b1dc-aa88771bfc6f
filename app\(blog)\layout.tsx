import ShopHeader from "@/components/Header/ShopHeader";
import ShopNavbar from "@/components/Header/ShopNavbar";
import Footer from "@/components/UI/Footer";
import type{ ReactNode } from "react";
import type { Metada<PERSON> } from 'next';
import "@/styles/styles.css"


type Props = {
    children: ReactNode;
}

export default function layout({children}: Props) {
    return (
        <>
            <ShopNavbar />
            {/* <ShopHeader /> */}
            {children}
            <Footer />            
        </>
    );
}
