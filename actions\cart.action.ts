import { Variation } from "@/lib/types/product.types";

export async function addToCard(varient:Variation) {
    try {
        const response = await fetch("http://localhost:8000/api/v1/cart", {
            method: "POST",
            headers: {
                "Authorization": "matin",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                "variant_id": varient.id,
                "quantity": 1
            })
        });
        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
export async function decreaseFromCartAction(varient:string) {
    try {
        const response = await fetch(`http://localhost:8000/api/v1/cart/${varient}`, {
            method: "DELETE",
            headers: {
                "Authorization": "matin",
                "Content-Type": "application/json"
            }            
        });
        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
export async function getUserCart() {
    try {
        const response = await fetch("http://localhost:8000/api/v1/cart", {
            method: "GET",
            headers: {
                "Authorization": "matin",
                "Content-Type": "application/json"
            }
        });
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
interface CartItems {
    variant_id: string
    quantity: number
}
export async function addMultipleToCart(cartItems: CartItems[]) {
    try {
        const response = await fetch("http://localhost:8000/api/v1/bulk-cart", {
            method: "POST",
            headers: {
                "Authorization": "matin",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                items: cartItems
            })
        });
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}