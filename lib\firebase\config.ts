/**
 * Firebase configuration
 * This file exports the Firebase configuration from environment variables
 */

// Fallback values in case environment variables are not set
const fallbackConfig = {
  apiKey: "AIzaSyCRAAOAiMfhgZylH2zHKIklwObtLN2iNco",
  authDomain: "khodrox-fb6ad.firebaseapp.com",
  projectId: "khodrox-fb6ad",
  storageBucket: "khodrox-fb6ad.firebasestorage.app",
  messagingSenderId: "442127983166",
  appId: "1:442127983166:web:1798e640b22d0f160de76f",
  measurementId: "G-CM6BYT6KHY"
};

const fallbackVapidKey = "BPlXoltdvMUKpFH4GpJTLSd4G-mM1SZTh7nDZ3nB1CL4xNGyx_yRSiOwQNy9aBD8BJlaWBL9WcJVb8alEaNdNEA";

// Get the Firebase configuration from environment variables with fallbacks
export const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || fallbackConfig.apiKey,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || fallbackConfig.authDomain,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || fallbackConfig.projectId,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || fallbackConfig.storageBucket,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || fallbackConfig.messagingSenderId,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || fallbackConfig.appId,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || fallbackConfig.measurementId
};

export const firebaseVapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || fallbackVapidKey;
