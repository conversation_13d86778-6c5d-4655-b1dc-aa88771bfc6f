import ShowAllSection from '../Services/ShowAllSection'
import CustomButton from '../UI/CustomButton'
import DoubleArrowIcon from '../common/svg/DoubleArrowIcon'
import ChevronLeft from '../common/svg/ChevronLeft'
import ChevronRight from '../common/svg/ChevronRight'
import {Star} from 'lucide-react'
import Image from 'next/image'
import Card from '../common/Card'


const products = [
    {
        title: "عنوان محصول",
        price: "80000000"
    },
    {
        title: "عنوان محصول",
        price: "80000000"
    },
    {
        title: "عنوان محصول",
        price: "80000000"
    },
    {
        title: "عنوان محصول",
        price: "80000000"
    }
]
const ProductsSlider = () => {
    return (
        <div className="md:w-12/12 md:flex-nowrap flex items-center justify-between mt-10 flex-wrap w-full">
            <ShowAllSection>
                <div className='flex items-center'>
                    <div className='mx-5'>
                        <button className='bg-primary text-white p-3 rounded-full border-x border-gray-400 mx-1'>
                            <ChevronRight/></button>
                        <button className='bg-white p-3 rounded-full border-x border-gray-400 mx-4'><ChevronLeft/>
                        </button>
                    </div>
                    <CustomButton width='w-36' bgColor='bg-gray-400'
                                  className="flex gap-2 justify-center rounded-3xl"> مشاهده همه <DoubleArrowIcon/>
                    </CustomButton>
                </div>
            </ShowAllSection>
            <div className="w-8/12 mb-20 flex justify-between items-center">
                <Card/>
                <Card/>
                <Card/>
            </div>
        </div>
    )
}

export default ProductsSlider