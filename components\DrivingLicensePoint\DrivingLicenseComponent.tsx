import Container from "@/components/common/Container";
import {PageDescription} from "@/components/common/PageDescription";
import {DrivingLicensePointForm} from "@/components/DrivingLicensePoint/DrivingLicensePointForm";
import {ServiceStatusType} from "@/lib/types/types";


type Props = {
    status?: ServiceStatusType
    title: string
}

export default async function DrivingLicenseComponent({title,status = 'ACTIVE'}: Props) {

    return (
        <Container>
            <div className='w-full max-w-[553px]'>
                <PageDescription
                    title={title}
                    description='شماره گواهینامه یا کد ملی خود را وارد کرده تا از وضعیت گواهینامه خود مطلع شوید.'
                />
                <DrivingLicensePointForm status={status}/>
            </div>
        </Container>
    );
}
