import { ChevronUp } from 'lucide-react'
// import Subtraction from "@/public/assets/images/subtraction.png"

import Image from 'next/image'
import CartIcon from '@/components/common/svg/CartIcon'
import "@/styles/styles.css"
import PriceRangeFilter from '@/components/shop/mainPage/PriceRangeFilter'
import SortingFilter from '@/components/shop/mainPage/SortBy'
import SearchFilter from '@/components/shop/mainPage/SearchFilter'
import CategoryFilter from '@/components/shop/mainPage/CategoryFilter'
import AvailableProductsFilter from '@/components/shop/mainPage/AvailableProductsFilter'
import WarrantyFilter from '@/components/shop/mainPage/WarrantyFilter'
import SelectBrand from '@/components/shop/mainPage/SelectBrand'
import SelectColor from '@/components/shop/mainPage/SelectColor'
import ConditionalFilter from '@/components/shop/mainPage/ConditionalFilter'
import PriceRange from '@/components/shop/mainPage/PriceRange'
import Product from '@/components/shop/mainPage/Product'
import Pagination from '@/components/UI/Pagination'
const ShopMainPage = () => {
  return (

    <div className='mb-10 flex flex-col min-h-screen max-w-7xl mx-auto'>


      {/* <div className='grid grid-cols-8 gap-6 max-w-7xl mx-auto mt-6 h-20'>
        <div className='col-span-2 bg-white flex justify-between items-center gap-3 rounded-3xl px-2'>
          <span>فیلترها</span>
          <button className='bg-[#FF4A4A] text-white px-4 py-3 rounded-3xl flex flex-row-reverse gap-2 text-sm items-center'>پاک کردن <Trash2 /> </button>
        </div>
        <div className='bg-white col-span-6 gap-7 flex items-center px-3 rounded-3xl'>
          <div className='flex gap-3 items-center'>
            <SlidersHorizontal />
            مرتب سازی بر اساس
          </div>
          <ul className='flex justify-between gap-10'>
            <li>
              جدیدترین
            </li>
            <li>
              ارزان ترین
            </li>
            <li>
              گران ترین
            </li>
            <li>
              پرفروش ترین
            </li>
            <li>
              پر بازدید ترین
            </li>
          </ul>
          <span className='mr-20'>214 محصول</span>
        </div>

      </div> */}
      <SortingFilter />
      <div className='flex items-start justify-between gap-8 max-w-7xl mx-auto mt-6 flex-grow'>
        {/* Sidebar */}
        <div className='md:col-span-2 flex flex-col gap-3 max-md:hidden w-[32%]'>
          <SearchFilter />
          <PriceRange />
          <CategoryFilter />
          <AvailableProductsFilter />
          <WarrantyFilter />
          <SelectBrand />
          <SelectColor />
          <ConditionalFilter />
        </div>

        {/* Product list */}
        <div className='md:col-span-6 max-md:col-span-12 flex flex-col w-full max-md:px-3'>
          {/* Products grid */}
          <div className='flex flex-wrap justify-between gap-4'>
            {/* <Product />
            <Product />
            <Product />
            <Product />
            <Product />
            <Product /> */}
          </div>

          {/* Pagination pushed to bottom */}
          <div className='mt-auto w-full overflow-x-hidden pt-6 self-center'>
            <Pagination />
          </div>
        </div>
      </div>

    </div>
  )
}

export default ShopMainPage