import {ChevronLeft} from 'lucide-react'

type Props = {
    width?: number
    height?: number
}

export default function ResultDetailsLeftIcon({width, height}: Props) {
    return (
        <div
            className='w-[30px] h-[30px] rounded-full bg-[#F3F4F6] border border-[#FFFFFF] flex justify-center items-center'>
            <ChevronLeft className='text-[#555A6A]' size={15}/>
        </div>
    );
}
