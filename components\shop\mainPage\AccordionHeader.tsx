"use client";
import { useEffect, useState } from "react";
import { ChevronUp, ChevronDown } from "lucide-react";

interface AccordionHeaderProps {
  title: string;
  children: React.ReactNode;
  titleSize?: string
}

const AccordionHeader: React.FC<AccordionHeaderProps> = ({ title, children, titleSize }) => {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    if (window.innerWidth < 768 && title !=="محدوده قیمت") {
      setIsOpen(false);
    }
  }, []);

  return (
    <>
      <div
        className="flex justify-between items-baseline pb-3 cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className={`${titleSize || "text-base"} relative py-2.5`}>{title}</h3>
        <button className="bg-gradient-to-t from-gray-100 to-transparent rounded-full pt-5 h-16 flex items-center justify-center p-2 transition-transform duration-300">
          {isOpen ? <ChevronUp className="w-6 h-6" /> : <ChevronDown className="w-6 h-6" />}
        </button>
      </div>

      
      <div
        className={`transition-max-height duration-300 overflow-hidden ${
          isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div className="mt-5">{children}</div>
      </div>
    </>
  );
};

export default AccordionHeader;
