import CheckoutProgress from "@/components/shop/checkout/CheckoutProgress"
import PaymentPageClient from "@/components/shop/checkout/PaymentPageClient"
import { CartApiResponse } from "@/lib/context/cart-context"
import { getShoppingCart } from "@/lib/services/productService"
type PageProps = {
  searchParams: Promise<{
    address?: string;
  }>
}
const CheckoutPaymentPage = async ({ searchParams }: PageProps) => {
  const {address} = await searchParams
  const OrderSummaryList:CartApiResponse = await getShoppingCart()
  console.log(address);
  
  return (
    <main className='container mx-auto mb-16'>
      <CheckoutProgress
        steps={[
          { title: 'سبد خرید', status: 'completed' },
          { title: 'انتخاب آدرس', status: 'completed' },
          { title: 'پرداخت', status: 'current' },
        ]}
      />

      <PaymentPageClient addressId={address} orderSummaryData={OrderSummaryList.data} />
    </main>
  )
}

export default CheckoutPaymentPage