'use client'

import { useEffect, useRef, useState } from 'react'
import CustomButton from '@/components/UI/CustomButton'
import {  ShoppingCart } from 'lucide-react'
import { useCart } from '@/lib/context/cart-context'
import { useAuth } from '@/lib/hooks/useAuth'
import { useRouter } from 'nextjs-toploader/app'
import toast from 'react-hot-toast'
type FactorCardProps = {
  paymentMethod?: string;
  steps: {
    nextStepBtnTitle: string;
    nextStepBtnLink: string;
    title: string
  };
  onCreateInvoice?: () => void;
  selectedAddress?: string
}

const FactorCard = ({ paymentMethod = 'wallet', steps, onCreateInvoice, selectedAddress }: FactorCardProps) => {
  const cardRef = useRef<HTMLDivElement | null>(null)
  const [showStickyBar, setShowStickyBar] = useState(false)
  const { finalPrice, totalDiscount, totalPrice } = useCart()
  const { userData } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setShowStickyBar(!entry.isIntersecting),
      {
        root: null,
        threshold: 0.1,
      }
    )

    const currentRef = cardRef.current
    if (currentRef) observer.observe(currentRef)

    return () => {
      if (currentRef) observer.unobserve(currentRef)
    }
  }, [])
  const handleNextStep = () => {
    if (steps.title == "shipping") {
      if (!selectedAddress) {
        toast.error("لطفا آدرس خود را انتخاب یا وارد کنید")
        return
      }
    }
    router.push(steps.nextStepBtnLink)
  }

  return (
    <>
      {/* Sticky Mini Cart for Mobile */}
      {showStickyBar && (
        <div className="fixed bottom-0 left-0 right-0 z-50 md:hidden bg-white border-t shadow-lg px-4 py-3 flex flex-col gap-5 items-center justify-between">
          <div className="flex items-center gap-3 text-sm font-medium">
            <ShoppingCart className="w-5 h-5 text-primary" />
            <span>جمع: {finalPrice ? finalPrice?.toLocaleString() : "-"} تومان</span>
          </div>
          <CustomButton className="text-sm px-4 py-2 rounded-xl">
            {steps.nextStepBtnTitle}
          </CustomButton>
        </div>
      )}

      {/* Full Cart */}
      <div
        ref={cardRef}
        className="max-md:w-full md:w-[25%] bg-white rounded-3xl p-5 h-[30rem] cart-circles half-circle"
      >
        <div className="relative title-bt-border w-fit pb-5 mt-5">
          <h2 className='max-md:text-base'>مجموع کل سبد خرید</h2>
        </div>

        <div className="flex justify-between mt-7 border-b-2 pb-4 max-md:text-sm">
          <span>قیمت کالاها</span>
          <span>{totalPrice ? totalPrice?.toLocaleString() : "-"} تومان</span>
        </div>

        <div className="flex justify-between mt-7 border-dashed border-b-2 pb-6 text-red-400 max-md:text-sm">
          <span>تخفیف</span>
          <span>{totalDiscount ? totalDiscount?.toLocaleString() : "-"} تومان</span>
        </div>

        <div className="mt-7 max-md:text-sm">
          <div className="flex justify-between">
            <span>قابل پرداخت</span>
            <span>{finalPrice ? finalPrice?.toLocaleString() : "-"} تومان</span>
          </div>

          {
            steps.title == "payment" ? (
              <div className="w-[95%] mx-auto mt-7">
                <CustomButton onClick={onCreateInvoice}  className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm">
                  {paymentMethod === 'wallet' ? 'پرداخت از کیف پول' : 'پرداخت آنلاین'}
                </CustomButton>
                <p className="text-sm mt-5 text-justify text-gray-500 max-md:text-xs max-md:leading-5">
                  {paymentMethod === 'wallet'
                    ? userData?.balance && userData.balance > 0
                      ? `موجودی کیف پول شما ${userData.balance.toLocaleString()} تومان است.`
                      : 'موجودی کیف پول شما کافی نیست. لطفا از پرداخت آنلاین استفاده کنید.'
                    : 'پرداخت از طریق درگاه بانکی انجام خواهد شد.'
                  }
                </p>
              </div>
            ) : 
            <div className="w-[95%] mx-auto mt-7">
                <CustomButton onClick={handleNextStep} className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm">
                  {steps.nextStepBtnTitle}
                </CustomButton>
              </div>
          }
          {/* <div className="w-[95%] mx-auto mt-7">
            <CustomButton className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm">
              {paymentMethod === 'wallet' ? 'پرداخت از کیف پول' : 'پرداخت آنلاین'}
            </CustomButton>
            <p className="text-sm mt-5 text-justify text-gray-500 max-md:text-xs max-md:leading-5">
              {paymentMethod === 'wallet'
                ? userData?.balance && userData.balance > 0
                  ? `موجودی کیف پول شما ${userData.balance.toLocaleString()} تومان است.`
                  : 'موجودی کیف پول شما کافی نیست. لطفا از پرداخت آنلاین استفاده کنید.'
                : 'پرداخت از طریق درگاه بانکی انجام خواهد شد.'
              }
            </p>
          </div> */}
        </div>
      </div>
    </>
  )
}

export default FactorCard
