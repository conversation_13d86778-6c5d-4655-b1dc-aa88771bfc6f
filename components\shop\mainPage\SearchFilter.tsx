import { SearchIcon } from 'lucide-react'
import React from 'react'
import AccordionHeader from './AccordionHeader'

const SearchFilter = () => {
    return (
        <div className='bg-white min-h-20 rounded-3xl p-3 search-products-filter max-md:hidden'>
            <AccordionHeader title='جستجو بین محصولات'>
                <div className='mt-5'>
                    <div className="hidden md:block relative ">
                        <input
                            type="text"
                            placeholder="دنبال چه چیزی میگردی؟"
                            className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100 rounded-full outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                        />
                        <SearchIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 text-lg" />
                    </div>

                </div>
            </AccordionHeader>
        </div>
    )
}

export default SearchFilter