html {
    scroll-behavior: smooth;
}

@media screen and (min-width: 500px) {
    .services-items li {
        width: 14%;
    }

    .text-bg {
        position: relative;
        padding-right: 65px;
        margin-right: 1px;
    }

    .text-bg::before {
        content: "";
        position: absolute;
        top: 5%;
        right: 0;
        width: 65px;
        height: 75%;
        background-image: url("/assets/images/pluses.png");
        background-repeat: repeat-y;
        background-position: right;
        z-index: -1;
    }

}

.title-with-pluses {
    position: relative;
    padding-right: 40px;
}

.title-with-pluses::before {
    content: "";
    position: absolute;
    top: 50%;
    right: -20px;
    transform: translateY(-50%);
    width: 30px;
    height: 15px;
    background-image: url("/assets/images/pluses-3.png");
    background-repeat: no-repeat;
    background-size: contain;
}

.increase-amount {
    position: relative;
}

.increase-amount::before {
    position: absolute;
    content: "";
    background-color: #F7BC06;
    width: 2px;
    top: 0;
    right: -10px;
    height: 35px;
}

.footer {
    background-image: url("/assets/images/fade-logo.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 350px 200px;
}

.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);

}

.dashboard-sidbar ul li a div {
    display: flex;
    gap: 10px;
    font-size: 14px;
    align-items: center;
}

.dashboard-sidbar ul li a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* height: 22px; */
    height: 100%;
}

.dashboard-sidbar ul li {
    height: 32px;

}

.dashboard-sidbar ul li a svg {
    display: flex;
    align-items: center;
}

.active {
    position: relative;
}


.dashboard-sidbar ul li span svg {
    width: 18px;
}

.active span svg {
    color: #1F84FB;
}

.title-border {
    padding-right: 10px;
    border-right: 3px solid #1F84FB;
}

.card-details h3 {
    position: relative;
}

.card-details h3::before {
    position: absolute;
    content: '';
    background-color: #1F84FB;
    height: 2px;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 80px;
}

.card-details h3::after {
    position: absolute;
    content: '';
    background-color: #ebebeb;
    height: 2px;
    right: 0;
    bottom: 0;
    width: 200px;
}

.search-products-filter h3::before {
    position: absolute;
    content: '';
    background-color: #F7BC06;
    height: 3px;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 40%;
}

.search-products-filter h3::after {
    position: absolute;
    content: '';
    background-color: #ebebeb;
    height: 3px;
    right: 0;
    bottom: 0;
    width: 100%;
}

.title-bt-border::before {
    position: absolute;
    content: '';
    background-color: #F7BC06;
    height: 3px;
    right: 0;
    bottom: 0;
    z-index: 10;
    width: 40%;
}

.title-bt-border::after {
    position: absolute;
    content: '';
    background-color: #ebebeb;
    height: 3px;
    right: 0;
    bottom: 0;
    width: 100%;
}

.custom-checkbox {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.custom-checkbox input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid #cccfdb;
    border-radius: 4px;
    display: inline-block;
    position: relative;
    background-color: white;
    transition: all 0.2s ease;
    cursor: pointer;
}

.custom-checkbox input[type="checkbox"]:checked {
    background-color: #07f;
    border-color: #07f;
}

.custom-checkbox input[type="checkbox"]::after {
    content: "";
    position: absolute;
    top: 3px;
    left: 5px;
    width: 4px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.custom-checkbox input[type="checkbox"]:checked::after {
    opacity: 1;
}


.product-options li {
    background-color: #FFFFFF;
    padding: 10px;
    border-radius: 50%;
    transition: all .1s ease-in;
    cursor: pointer;
}

.product-options li:hover {
    background-color: #F7BC06;
    color: #FFFFFF;
}

.product-details div {
    display: flex;
    flex-direction: column;
    background-color: #F9FAFB;
    width: 30%;
    padding: 8px 12px;
    margin-bottom: 10px;
    border-radius: 15px;
    gap: 5px;
}

.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    height: auto !important;
}

.active-tab {
    background-color: #1F84FB;
    color: #fff;
    padding: 7px 10px;
    border-radius: 12px;
}

.pluses {
    position: relative;
}

.pluses::before {
    content: "";
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    zoom: 230%;
    width: 25px;
    height: 15px;
    background-image: url("/assets/images/pluses-3.png");
    background-repeat: no-repeat;
    background-size: contain;
}

.payment-card-body div {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    /* margin-right: 5px;
    margin-left: 5px; */
}

.payment-card-body>div:nth-child(odd) {
    background: #f8f8f8;
    border-radius: 10px;
    padding: 6px 5px;
}

.swiper {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.swiper-initialized {
    opacity: 1;
}

.cart-circles {
    background-image: url("/assets/images/cart-circles.webp");
    background-repeat: no-repeat;
}

.half-circle {
    position: relative;

    overflow: hidden;
}

.half-circle::after {
    content: "";
    position: absolute;
    width: 65px;
    height: 45;
    background-color: #F5F6F8;
    border-radius: 50%;
    top: 205px;
    left: -51px;
}

.half-circle::before {
    content: "";
    position: absolute;
    width: 65px;
    height: 45px;
    background-color: #F5F6F8;
    border-radius: 50%;
    top: 205px;
    right: -51px;
}

.half-circle-payment-card {
    position: relative;

    /* overflow: hidden; */
}

.half-circle-payment-card::after {
    content: "";
    position: absolute;
    width: 80px;
    height: 75;
    background-color: #F5F6F8;
    border-radius: 50%;
    top: 535px;
    left: -55px;
    /* z-index: 100; */
}

.half-circle-payment-card::before {
    content: "";
    position: absolute;
    width: 80px;
    height: 75px;
    background-color: #F5F6F8;
    border-radius: 50%;
    top: 535px;
    right: -51px;
}

table {
    width: 100%;
    border: 1px solid #d1d5db;
    /* tailwind's gray-300 */
    background-color: #ffffff;
    font-size: 0.875rem;
    /* text-sm */
    border-collapse: collapse;
}

table th,
table td {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: center;
}

table th {
    background-color: #e5e7eb;
    /* tailwind's gray-200 */
}

table tbody tr:nth-child(odd) {
    background-color: #f3f4f6;
    /* tailwind's gray-100 */
}

@media (min-width: 768px) {
    .product-w-32 {
        width: 32%;
    }

    .about-service h2 {
        font-weight: 900;
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .about-service p {
        line-height: 2rem;
    }

    .about-service div h2 {
        line-height: 2rem;
    }

}


.checkbox-wrapper-15 .cbx {
    -webkit-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
}

.checkbox-wrapper-15 .cbx span {
    display: inline-block;
    vertical-align: middle;
    transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-15 .cbx span:first-child {
    position: relative;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transform: scale(1);
    vertical-align: middle;
    border: 1px solid #B9B8C3;
    transition: all 0.2s ease;
}

.checkbox-wrapper-15 .cbx span:first-child svg {
    position: absolute;
    z-index: 1;
    top: 8px;
    left: 6px;
    fill: none;
    stroke: white;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 16px;
    stroke-dashoffset: 16px;
    transition: all 0.3s ease;
    transition-delay: 0.1s;
    transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-15 .cbx span:first-child:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #506EEC;
    display: block;
    transform: scale(0);
    opacity: 1;
    border-radius: 50%;
    transition-delay: 0.2s;
}

.checkbox-wrapper-15 .cbx span:last-child {
    margin-left: 8px;
}

.checkbox-wrapper-15 .cbx span:last-child:after {
    content: "";
    position: absolute;
    top: 8px;
    left: 0;
    height: 1px;
    width: 100%;
    /* background: #B9B8C3; */
    transform-origin: 0 0;
    transform: scaleX(0);
}

.checkbox-wrapper-15 .cbx:hover span:first-child {
    border-color: #3c53c7;
}

.checkbox-wrapper-15 .inp-cbx:checked+.cbx span:first-child {
    border-color: #3c53c7;
    background: #3c53c7;
    animation: check-15 0.6s ease;
}

.checkbox-wrapper-15 .inp-cbx:checked+.cbx span:first-child svg {
    stroke-dashoffset: 0;
}

.checkbox-wrapper-15 .inp-cbx:checked+.cbx span:first-child:before {
    transform: scale(2.2);
    opacity: 0;
    transition: all 0.6s ease;
}

.checkbox-wrapper-15 .inp-cbx:checked+.cbx span:last-child {
    color: #B9B8C3;
    transition: all 0.3s ease;
}

.checkbox-wrapper-15 .inp-cbx:checked+.cbx span:last-child:after {
    transform: scaleX(1);
    transition: all 0.3s ease;
}

.full-border-bottom {
    position: relative;
    padding-bottom: 20px;
}

.full-border-bottom::before {
    position: absolute;
    content: "";
    background-color: #f7bc06;
    height: 3px;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 7%;
}

.full-border-bottom::after {
    position: absolute;
    content: "";
    background-color: #ebebeb;
    height: 3px;
    right: 0;
    bottom: 0;
    width: 100%;
}
.learn-more-title::before {
    position: absolute;
    content: "";
    background-color: #000;
    height: 1px;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 12%;
}

.learn-more-title::after {
    position: absolute;
    content: "";
    background-color: #fff;
    height: 1px;
    right: 0;
    bottom: 0;
    width: 30%;
}

.hollow-circle {
    position: relative;
  }
  
  .hollow-circle::before {
    content: '';
    position: absolute;
    top: 0.3em; /* Adjust vertically to align with text */
    right: 0;
    width: 0.6em;
    height: 0.6em;
    border: 2px solid #1F84FB;
    border-radius: 50%;
    background: transparent;
    display: inline-block;
  }

  .sidebar-title::before {
    content: '';
    background-color: #F7BC06;
    position: absolute;
    height: 5px;
    width: 12px;
    right: 0;
    top: 10;
  }


@keyframes check-15 {
    50% {
        transform: scale(1.2);
    }
}

@media not all and (width >=768px) {

    .about-service p {
        font-size: 15px;
        line-height: 28px;
    }

    .about-service h3 {
        font-size: 1.125rem;
    }

    .pluses::before {
        display: none;
    }

    .product-details div {
        width: 45%;
    }

    .half-circle-payment-card::after {
        width: 67px;
        height: 65;
        top: 600px;
        left: -51px;
        /* z-index: 100; */
    }

    .half-circle-payment-card::before {
        width: 67px;
        height: 65px;
        top: 600px;
        right: -51px;
    }
}