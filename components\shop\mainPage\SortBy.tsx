"use client";
import { useEffect, useRef, useState } from "react";
import { ArrowDownNarrowWide, ChevronDown, SlidersHorizontal, Trash2 } from "lucide-react";
import NavbarItems from "@/components/Header/NavbarItems";
import MobileFilterDrawer from "./MobileFilterDrawer";

const SortingFilter = () => {
    const [selectedSort, setSelectedSort] = useState("جدیدترین");
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false); // State for mobile menu
    const menuRef = useRef<HTMLDivElement>(null); // Ref for the menu



    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);


    const sortingOptions = ["جدیدترین", "ارزان ترین", "گران ترین", "پرفروش ترین", "پر بازدید ترین"];

    return (
        <div className="flex justify-between gap-8 w-full max-md:w-full md:px-0 mx-auto mt-6 h-20 max-md:px-4 max-md:text-sm">
            {/* Filters Section */}
            
            <div className="w-[32%] bg-white flex justify-center md:justify-between items-center gap-6 max-md:text-sm rounded-3xl px-3 max-md:hidden">
                <span className="flex flex-row-reverse gap-2 items-center">فیلترها <ArrowDownNarrowWide className="md:hidden" /></span>

                <button className="bg-[#FF4A4A] max-md:hidden text-white px-4 py-3 rounded-3xl flex flex-row-reverse gap-2 text-sm items-center">
                    پاک کردن <Trash2 className="w-5" />
                </button>
               
            </div>
            <div onClick={() => setIsMenuOpen(true)} className="md:col-span-2 bg-white flex justify-center md:justify-between items-center gap-6 max-md:text-sm rounded-3xl px-3 md:hidden">
                <span className="flex flex-row-reverse gap-2 items-center">فیلترها <ArrowDownNarrowWide className="md:hidden" /></span>
                <span  className={`bg-gradient-to-t from-gray-100 to-transparent md:hidden rounded-full p-1 transition-transform duration-200 `}>
                    <ChevronDown />
                </span>
            </div>





            <div
                ref={menuRef}
                className={`md:hidden fixed top-0 right-0 h-full w-[80%] bg-white shadow-lg transform transition-transform duration-200 ease-in z-50 flex flex-col ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'
                    }`}
            >
                {/* Close Button */}
                <div className="flex w-[95%] mt-4 justify-end z-[100] mb-10">
                    <button
                        onClick={() => setIsMenuOpen(false)}
                        className="text-gray-600 hover:text-black text-3xl font-bold"
                        aria-label="Close filter drawer"
                    >
                        &times;
                    </button>
                </div>

                {/* Scrollable content below the close button */}
                <div className="overflow-y-auto flex-1 px-3 pb-6">
                    
                    <MobileFilterDrawer />
                </div>
            </div>


            {isMenuOpen && (
                <div
                    className='fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden'
                    onClick={() => setIsMenuOpen(false)}
                />
            )}


            {/* Sorting Section */}
            <div className="bg-white w-full md:col-span-9 gap-7 flex items-center px-3 rounded-3xl relative">
                {/* Desktop Sorting */}
                <div className="hidden md:flex gap-3 items-center text-sm">
                    <SlidersHorizontal />
                    مرتب سازی بر اساس
                </div>

                {/* Desktop Sorting List */}
                <ul className="hidden md:flex justify-between gap-4 text-sm">
                    {sortingOptions.map((option) => (
                        <li
                            key={option}
                            className={`cursor-pointer px-2.5 py-3 rounded-3xl ${selectedSort === option ? "bg-primary font-bold text-white" : ""}`}
                            onClick={() => setSelectedSort(option)}
                        >
                            {option}
                        </li>
                    ))}
                </ul>

                {/* Mobile Dropdown */}
                <div className="md:hidden relative w-full">
                    <button
                        className="w-full  px-2 py-2 rounded-3xl flex justify-between items-center text-sm"
                        onClick={(e) => {
                            e.stopPropagation(); // Prevent event bubbling
                            setDropdownOpen(!dropdownOpen);
                        }}
                    >
                        <SlidersHorizontal />
                        {selectedSort}
                        <span className={`bg-gradient-to-t from-gray-100 to-transparent rounded-full p-1 transition-transform duration-200 ${dropdownOpen ? "rotate-180" : "rotate-0"}`}>
                            <ChevronDown />
                        </span>
                    </button>

                    {dropdownOpen && (
                        <ul
                            className="absolute right-0 mt-2 w-full bg-white shadow-md rounded-xl overflow-hidden z-20"
                            onClick={(e) => e.stopPropagation()} // Prevents closing when clicking inside
                        >
                            {sortingOptions.map((option) => (
                                <li
                                    key={option}
                                    className="px-4 py-2 hover:bg-gray-200 cursor-pointer z-20"
                                    onClick={() => {
                                        setSelectedSort(option);
                                        setDropdownOpen(false);
                                    }}
                                >
                                    {option}
                                </li>
                            ))}
                        </ul>
                    )}
                </div>

                <span className="bg-gradient-to-l from-gray-100 to-transparent hidden md:block mr-20 p-3 py-4 rounded-3xl">214 محصول</span>
            </div>
        </div>
    );
};

export default SortingFilter;
