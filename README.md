# Khodrox - Modern Web Application

Khodrox is a modern web application built with Next.js, React, and Tailwind CSS. The application appears to be a comprehensive platform for vehicle-related services, including a blog, e-commerce functionality, and various inquiry services.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Folder Structure](#folder-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
- [Component Documentation](#component-documentation)
  - [Blog Components](#blog-components)
  - [Inquiry Components](#inquiry-components)
  - [Common Components](#common-components)
  - [UI Components](#ui-components)
  - [Header Components](#header-components)
  - [Dashboard Components](#dashboard-components)
  - [Wallet Components](#wallet-components)
  - [Payment Components](#payment-components)
  - [Shop Components](#shop-components)
- [Firebase Configuration](#firebase-configuration)
  - [Overview](#overview)
  - [Configuration Files](#configuration-files)
  - [Service Worker](#service-worker)
  - [Notification Components](#notification-components)
    - [Firebase Notification Service](#firebase-notification-service)
    - [SendNotification Component](#sendnotification-component)
      - [Key Features](#key-features)
      - [Component Structure](#component-structure)
      - [Implementation Details](#implementation-details)
      - [Usage](#usage)
      - [Customization](#customization)
      - [Firebase Message Payload](#firebase-message-payload)
      - [Sending Test Notifications](#sending-test-notifications)
      - [Handling Notifications](#handling-notifications)
  - [Server Actions for Notifications](#server-actions-for-notifications)
  - [Best Practices and Advanced Usage](#best-practices-and-advanced-usage)
  - [Setup for New Developers](#setup-for-new-developers)
  - [Production Deployment](#production-deployment)
  - [Troubleshooting](#troubleshooting)

## Features

- **Blog System**: A comprehensive blog with categories, search functionality, and article recommendations.
- **E-commerce**: Product listings, checkout process, and payment integration.
- **User Dashboard**: User account management and service history.
- **Vehicle Services**: Various vehicle-related services including:
  - Car and motor violation tickets inquiry.
  - Driving license status.
  - Vehicle documentation.
  - Insurance services.

## Tech Stack

- **Framework**: Next.js (App Router).
- **UI Library**: React.
- **Styling**: Tailwind CSS.
- **Form Handling**: React Hook Form with Zod validation.
- **State Management**: React Context API.
- **Icons**: Lucide React.
- **Notifications**: React Hot Toast.
- **Push Notifications**: Firebase Cloud Messaging (FCM).

## Folder Structure

```
khodrox/
├── actions/                  # Server actions for data fetching and mutations
├── app/                      # Next.js app router pages
│   ├── (blog)/               # Blog-related pages
│   ├── (dashboard)/          # User dashboard pages
│   ├── (khodrox)/            # Main service pages
│   │   ├── (inquiry)/        # Inquiry service pages
│   │   └── ...               # Other service pages
│   └── (shop)/               # E-commerce pages
├── components/               # React components
│   ├── blog/                 # Blog-related components
│   │   └── SinglePage/       # Blog post page components
│   ├── common/               # Shared/common components
│   │   └── svg/              # SVG icon components
│   ├── Dashboard/            # Dashboard components
│   ├── Header/               # Header components
│   ├── inquiry/              # Inquiry form components
│   │   └── result/           # Inquiry result components
│   ├── InquiryStaticComponents/ # Static components for inquiry pages
│   ├── shop/                 # E-commerce components
│   ├── UI/                   # UI components (buttons, inputs, etc.)
│   └── Wallet/               # Wallet and payment components
├── lib/                      # Utility functions, hooks, and types
│   ├── constants/            # Application constants
│   ├── firebase/             # Firebase configuration
│   ├── hooks/                # Custom React hooks
│   ├── notification/         # Notification services
│   ├── types/                # TypeScript type definitions
│   └── utils.ts              # Utility functions
├── public/                   # Static assets
│   ├── assets/
│   │   └── images/           # Image assets
│   ├── firebase-config.js    # Generated Firebase configuration
│   └── firebase-messaging-sw.js # Firebase service worker
├── scripts/                  # Utility scripts
└── styles/                   # Global styles
```

## Getting Started

### Prerequisites

- Node.js (v18 or later).
- npm or yarn.

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/khodrox.git
   cd khodrox
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit the .env file with your configuration values
   ```

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Component Documentation

### Blog Components

The blog system includes various components for displaying blog posts, categories, and related content:

- `BlogHead`: Displays the blog post header with title and metadata.
- `BlogPostCategory`: Shows the blog post categories.
- `BlogPostTableOfContent`: Displays a table of contents for the blog post.
- `RecommendedPost`: Shows a recommended blog post with image and title.
- `BlogSearchBox`: Provides search functionality for the blog.
- `ArticleSlider`: Displays a slider of related articles.
- `NewArticlesItem`: Shows a preview of a new article.

### Inquiry Components

The inquiry system allows users to search for various vehicle-related information:

- `InquiryComponent`: Main component for inquiry forms.
- `InquiryForm`: Form component for submitting inquiries.
- `InquiryResultWithDetails`: Displays inquiry results with detailed information.

### Common Components

These components are shared across multiple parts of the application:

- `Container`: A layout wrapper component with background styling.
- `Card`: A reusable card component with consistent styling.
- `ChoiceWrapper`: A selectable container for choice-based UI elements.
- `PageDescription`: Component for displaying page titles and descriptions.
- `ServiceIcon`: SVG icon component for various services.
- `SendNotification`: Component for requesting notification permissions.

### UI Components

Reusable UI components that provide consistent styling across the application:

- `Button`: Standard button component with various styles and variants.
- `CustomButton`: Extended button component with additional features like loading state.
- `Input`: Standardized input component with consistent styling.
- `Pagination`: Component for handling pagination in lists.
- `Footer`: Application footer with links and information.

### Header Components

Components related to the application header and navigation:

- `Navbar`: Main navigation component for the application.
- `ShopHeader`: Specialized header for the shop section.
- `ShopNavbar`: Navigation specifically for the shop section.
- `ServiceList`: Displays available services in the header.
- `CategoryMenu`: Dropdown menu for product categories.
- `UserLoggedInButton`: Button displayed when a user is logged in.
- `DashboardNavbar`: Navigation for the dashboard section.

### Dashboard Components

Components for the user dashboard interface:

- `UserQuickAccess`: Quick access tiles for common user actions.
- `WalletAmount`: Displays the user's wallet balance.
- `PopularServices`: Shows popular services in the dashboard.
- `UserLastInquiries`: Displays the user's recent inquiries.
- `FavoritesList`: Shows the user's favorite items.
- `LastOrders`: Displays the user's recent orders.

### Wallet Components

Components related to the wallet and payment functionality:

- `WalletBox`: Main container for wallet information.
- `WalletBalance`: Displays the current wallet balance.
- `PaymentBox`: Interface for adding funds to the wallet.
- `PaymentResult`: Shows the result of a payment transaction.

### Payment Components

Components for handling payment processing and displaying payment results:

- `PaymentStatusHeader`: Displays payment success/failure status with visual indicators.
- `PaymentDetails`: Shows detailed payment information in a structured format.
- `PaymentDetailRow`: Reusable component for displaying individual payment details.

#### Qabzino Payment Callback Page

The Qabzino payment callback page (`app/(khodrox)/payment/qabzino/callback/page.tsx`) handles the verification and display of payment results after a user completes a payment through the Qabzino payment gateway. This page processes the payment verification response and presents the results in a user-friendly format.

**Key Features:**
- Processes payment verification API responses.
- Displays payment status (success/failure) with appropriate visual indicators.
- Shows detailed payment information including amount, bill ID, and transaction details.
- Implements a clean, visually appealing UI with consistent styling.
- Responsive design for both desktop and mobile devices.

## Firebase Configuration

### Overview

The application uses Firebase Cloud Messaging (FCM) for push notifications. This section explains how the Firebase integration works and how to set it up for development or production.

### Configuration Files

#### 1. Environment Variables

Firebase configuration is stored in the `.env` file:

```
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key
```

#### 2. Firebase Config Module

Located at `lib/firebase/config.ts`, this file exports the Firebase configuration with fallbacks:

```typescript
export const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "fallback-api-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "fallback-auth-domain",
  // other config values
};
```

### Service Worker

The service worker handles background notifications and is located at `public/firebase-messaging-sw.js`:

```javascript
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-messaging-compat.js');
importScripts('/firebase-config.js');

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function(payload) {
  console.log('Received background message: ', payload);
});
```

### Notification Components

#### Firebase Notification Service

Located at `lib/notification/FirebaseNotificationService.js`, this service handles Firebase messaging:

```javascript
export class FirebaseNotificationService {
  constructor(config, vapidKey) {
    this.config = config;
    this.vapidKey = vapidKey;
  }

  async init() {
    const app = initializeApp(this.config);
    this.messaging = getMessaging(app);
    await Notification.requestPermission();
  }

  async getToken() {
    return await getToken(this.messaging, { vapidKey: this.vapidKey });
  }

  onForegroundMessage(callback) {
    onMessage(this.messaging, callback);
  }
}
```

### Best Practices and Advanced Usage

#### Storing the FCM Token

After obtaining the FCM token, you should send it to your server to associate it with the user:

```typescript
const token = await notifService.getToken();
await fetch('/api/users/me/notification-token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ token }),
});
```

#### Handling Token Refresh

Firebase may refresh the token periodically. You should handle this by setting up a token refresh listener:

```typescript
firebase.messaging().onTokenRefresh(async () => {
  try {
    const refreshedToken = await firebase.messaging().getToken();
    console.log('Token refreshed:', refreshedToken);
    await updateTokenOnServer(refreshedToken);
  } catch (error) {
    console.error('Unable to retrieve refreshed token:', error);
  }
});
```

#### Handling Notification Clicks

You can handle notification clicks to navigate to specific pages:

```javascript
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  const data = event.notification.data;
  if (data && data.url) {
    clients.openWindow(data.url);
  } else {
    clients.openWindow('/');
  }
});
```

#### Segmenting Users for Targeted Notifications

You can use Firebase Cloud Messaging topics to send notifications to specific user segments:

```typescript
async function subscribeToTopic(token, topic) {
  await fetch('/api/notifications/subscribe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token, topic }),
  });
}
```

#### Testing Notifications in Development

For testing notifications during development:

1. Use the Firebase Console to send test messages.
2. Create a simple API endpoint to send notifications:

```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import admin from 'firebase-admin';

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\n/g, '\n'),
    }),
  });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { token, title, body } = req.body;
    await admin.messaging().send({
      token,
      notification: {
        title,
        body,
      },
    });
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error sending notification:', error);
    return res.status(500).json({ error: 'Failed to send notification' });
  }
}
```

#### Implementing a Notification Preference Center

Consider adding a notification preference center to allow users to manage their notification settings:

```tsx
function NotificationPreferences() {
  const [preferences, setPreferences] = useState({
    marketing: true,
    updates: true,
    reminders: false,
  });

  const handleToggle = (key) => {
    setPreferences((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="notification-preferences">
      <h2>Notification Preferences</h2>
      <div className="preference-item">
        <label>Marketing Notifications</label>
        <Switch checked={preferences.marketing} onChange={() => handleToggle('marketing')} />
      </div>
    </div>
  );
}
```