"use client";
import { useEffect, useState } from "react";
import { ChevronUp, ChevronDown, Star } from "lucide-react";
import RoundedArrow from "@/components/common/svg/RoundedArrow";
import clsx from "clsx";

interface AccordionWrapperProps {
  title: string;
  icon?: React.ReactNode;
  roundedArrow?: boolean
  children: React.ReactNode;
  fullBorderBottom?: boolean
  className?: string
}

const AccordionWrapper: React.FC<AccordionWrapperProps> = ({ title, icon, children, roundedArrow = true, fullBorderBottom, className }) => {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    if (window.innerWidth < 768 && title !== "محدوده قیمت") {
      setIsOpen(false);
    }
  }, []);

  return (
    <>
      <div
        className={clsx("flex justify-between items-center py-3 max-md:items-start cursor-pointer overflow-visible ",
          fullBorderBottom ? 
          "full-border-bottom"
          :
          "",
          className ?? ""
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex max-md:flex-wrap items-center gap-3">
          {icon && <span className="text-gray-600">{icon}</span>}
          <h2 className="text-lg max-md:text-base relative px-2 border-r-[3px]">{title}</h2>
          {roundedArrow && <RoundedArrow className="max-md:w-12" />}
          { !roundedArrow && 
          <div className='review flex gap-2 items-center h-full md:my-2 max-md:mt-5 max-md:mx-auto'>
            <span className='text-sm'> 3 از 5 </span>
            <div className='flex gap-1 items-center h-full'>
              <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
              <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
              <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />

              <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' /> <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' />

            </div>
            <span className="text-xs"> امتیاز دریافت شده از کاربران </span>
          </div> }
        </div>
        <button className="bg-gradient-to-t from-gray-100 to-transparent rounded-full h-7 w-7 flex items-center justify-center p-1 transition-transform duration-300">
          {isOpen ? <ChevronUp className="w-6" /> : <ChevronDown className="w-6" />}
        </button>
      </div>

      <div
        className={`transition-max-height duration-300 overflow-hidden ${isOpen ? " opacity-100" : "max-h-0 opacity-0"
          }`}
      >
        <div className="mt-5 overflow-visible">{children}</div>
      </div>
    </>
  );
};

export default AccordionWrapper;
