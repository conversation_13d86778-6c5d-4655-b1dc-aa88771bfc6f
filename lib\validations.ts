import {carPlatePartsMaxLengths, plateAlphabets} from "@/components/UI/CarPlateInput";
import {
    CAR_PLATE_ALPHABET,
    CAR_PLATE_LEFT,
    CAR_PLATE_MIDDLE,
    CAR_PLATE_RIGHT,
    MOTOR_PLATE_LEFT,
    MOTOR_PLATE_RIGHT
} from "./constants";
import {motorPlatePartsMaxLengths} from "@/components/UI/MotorPlateInput";

export function plateNumberIsNotValid(plateNumber: string[], isMotor: boolean) {
    const value = plateNumber;
    if (!Array.isArray(value) || (!isMotor && value.length !== 4) || (isMotor && value.length !== 2)) {
        return true;
    }
    const leftValid = isMotor ? value[MOTOR_PLATE_LEFT]?.length === motorPlatePartsMaxLengths[MOTOR_PLATE_LEFT] : value[CAR_PLATE_LEFT]?.length === carPlatePartsMaxLengths[CAR_PLATE_LEFT]
    const rightValid = isMotor ? value[MOTOR_PLATE_RIGHT]?.length === motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT] : value[CAR_PLATE_RIGHT]?.length === carPlatePartsMaxLengths[CAR_PLATE_RIGHT]
    const middleValid = isMotor ? true : value[CAR_PLATE_MIDDLE]?.length === carPlatePartsMaxLengths[CAR_PLATE_MIDDLE]
    const alphabetValid = isMotor ? true : plateAlphabets.includes(value[CAR_PLATE_ALPHABET])

    return !(leftValid && rightValid && middleValid && alphabetValid);
}

export function isValidIranianNationalCode(code: string) {
    if (!/^\d{10}$/.test(code) || /^(\d)\1{9}$/.test(code)) return false;

    const digits = [...code].map(Number);
    const checkDigit = digits[9];

    // Compute checksum
    const sum = digits.slice(0, 9).reduce((acc, digit, i) => acc + digit * (10 - i), 0);
    const remainder = sum % 11;
    const expectedCheckDigit = remainder < 2 ? remainder : 11 - remainder;

    return checkDigit === expectedCheckDigit;
}
