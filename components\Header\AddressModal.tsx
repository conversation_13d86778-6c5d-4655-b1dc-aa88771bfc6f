'use client';

import {  useState } from 'react';
import { Pencil, Trash } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog';
import EditAddressForm from './EditAddress'; // (your separated EditAddressForm)
import { UserAddress } from '@/lib/types/types';

// type AddressItemType = {
//   id: number;
//   address: string;
//   details?: string;
//   title?: string;
//   phone?: string;
// };

// const addressesData: AddressItemType[] = [
//   { id: 1, address: 'اصفهان، جلفا، بوستان سعدی', title: 'منزل' },
//   { id: 2, address: 'سعادت آباد، سعادت آباد (آزادگان)، منصور رفیعیان', title: 'شرکت', details: 'تماس بگیرید', phone: '09103528525' },
//   { id: 3, address: 'بوشهر، باغ زهرا سامان، بلوار ۱۶', title: 'خانه دوم' },
//   { id: 4, address: 'اصفهان، جلفا، بوستان سعدی', title: 'منزل' },
//   { id: 5, address: 'سعادت آباد، سعادت آباد (آزادگان)، منصور رفیعیان', title: 'شرکت', details: 'تماس بگیرید', phone: '09103528525' },
//   { id: 6, address: 'بوشهر، باغ زهرا سامان، بلوار ۱۶', title: 'خانه دوم' },
// ];

function getShortAddress(fullAddress: string) {
  const words = fullAddress.split('،').map(word => word.trim());
  return words.slice(0, 2).join('، ');
}

export default function AddressModal({addressList}: {addressList: UserAddress[]}) {
  console.warn(addressList);
  
  const [addresses, setAddresses] = useState<UserAddress[]>(addressList);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(addresses[0]?.id || null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'select' | 'edit'>('select');
  const [addressToEdit, setAddressToEdit] = useState<UserAddress | null>(null);

  function handleUpdateAddress(updatedAddress: UserAddress) {
    if (updatedAddress.id) {
      // Update existing address
      setAddresses(prev =>
        prev.map(addr => (addr.id === updatedAddress.id ? updatedAddress : addr))
      );
    } else {
      // Create new address
      // const newAddress = { ...updatedAddress, id: Date.now() };
      // setAddresses(prev => [...prev, newAddress]);
      // setSelectedAddressId(newAddress.id);
    }
    setModalMode('select');
  }


  return (
    <>
      <button onClick={() => setIsModalOpen(true)} className="text-primary">
        مکان من: {getShortAddress(addresses.find(a => a.id === selectedAddressId)?.name || '')}
      </button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="w-[90vw] max-w-2xl rounded-2xl p-4">
          <DialogHeader>
            <DialogTitle className="text-center py-3">
              {modalMode === 'select' ? 'انتخاب آدرس' : 'ویرایش آدرس'}
            </DialogTitle>
          </DialogHeader>

          {modalMode === 'select' ? (
            <div className="flex flex-col gap-3 max-h-[580px] overflow-y-auto">
              {addresses.map((item) => (
                <div
                  key={item.id}
                  className={`border rounded-xl p-3 flex items-center justify-between cursor-pointer ${
                    selectedAddressId === item.id ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => setSelectedAddressId(item.id)}
                >
                  <div className="flex items-start gap-2">
                    <input
                      type="radio"
                      checked={selectedAddressId === item.id}
                      onChange={() => setSelectedAddressId(item.id)}
                    />
                    <div className="flex flex-col gap-2">
                      {item.receiver_name && <span className="font-semibold">{item.receiver_name}</span>}
                      {/* <span className="text-sm text-gray-600">{item.address}</span> */}
                      {item.address && <span className="text-xs text-gray-500">{item.address}</span>}
                      {item.receiver_phone && <span className="text-xs text-gray-500">{item.receiver_phone}</span>}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      className="text-green-500 hover:text-green-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        setAddressToEdit(item);
                        setModalMode('edit');
                      }}
                    >
                      <Pencil size={18} />
                    </button>
                    <button
                      type="button"
                      className="text-red-500 hover:text-red-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        setAddresses(prev => prev.filter(addr => addr.id !== item.id));
                        if (selectedAddressId === item.id) {
                          setSelectedAddressId(null);
                        }
                      }}
                    >
                      <Trash size={18} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <EditAddressForm
              address={addressToEdit}
              onCancel={() => setModalMode('select')}
              onSave={handleUpdateAddress}
            />
          )}

          {/* ساخت آدرس جدید Button */}
          {modalMode === 'select' && (
            <div className="flex justify-start mt-4">
              <button
                onClick={() => {
                  setAddressToEdit(null);
                  setModalMode('edit');
                }}
                className="flex items-center gap-2 text-green-600 font-bold"
              >
                <span className="text-2xl">+</span>
                ساخت آدرس جدید
              </button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
