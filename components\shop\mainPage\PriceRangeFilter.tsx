"use client"
import { useState } from "react";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";

const PriceRangeFilter = () => {
  const [values, setValues] = useState<[number, number]>([0, 169000000]);

  return (
    <div className="w-full max-w-xs mt-5 bg-white rounded-2xl rtl">


      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between p-2 rounded-lg">
          <span className="text-gray-500">از</span>
          {/* <input
            type="text"
            value={values[0].toLocaleString()}
            className="w-20 p-4 text-right bg-transparent outline-none"
            
          /> */}
          <div className="w-[70%] bg-[#f5f6f8] flex justify-between border-2 border-gray-100 rounded-2xl overflow-hidden text-sm">
            <span className="h-full bg-[#f5f6f8] p-3 w-[70%] overflow-hidden">
              {values[0].toLocaleString()}
            </span>
            <span className="text-gray-500 p-3 bg-white rounded-br-xl">تومان</span>

          </div>
        </div>

        <div className="flex items-center justify-between p-2  rounded-lg">
          <span className="text-gray-500">تا</span>
          {/* <input
            type="text"
            value={values[1].toLocaleString()}
            className="w-20 text-right bg-transparent outline-none"
            readOnly
          /> */}
           <div className="w-[70%] bg-[#f5f6f8] flex justify-between border-2 border-gray-100 rounded-2xl overflow-hidden text-sm">
            <span className="h-full bg-[#f5f6f8] p-3 w-[70%] overflow-hidden">
              {values[1].toLocaleString()}
            </span>
            <span className="text-gray-500 p-3 bg-white rounded-br-xl">تومان</span>

          </div>
          
        </div>
      </div>

      {/* Dual Handle Range Slider */}
      <div className="mt-4 px-2">
        <Slider
          range
          min={0}
          max={169000000}
          step={1000000}
          value={values}
          onChange={(val) => setValues(val as [number, number])}
          trackStyle={{ backgroundColor: "#2563eb", height: 6 }} // Blue track
          railStyle={{ backgroundColor: "#e5e7eb", height: 6 }} // Gray rail
          handleStyle={[
            { backgroundColor: "#facc15", borderColor: "#facc15", width: 16, height: 16 },
            { backgroundColor: "#2563eb", borderColor: "#2563eb", width: 16, height: 16 }
          ]}
        />
      </div>

      {/* Labels */}
      <div className="flex justify-between text-sm text-gray-500 mt-2">
        <span>گرانترین</span>
        <span>ارزانترین</span>
      </div>
    </div>
  );
};

export default PriceRangeFilter;


