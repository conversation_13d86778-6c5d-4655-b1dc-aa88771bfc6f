'use server'

import {
    ActionResult,
    AuthResponse,
    GetUserResponse,
    VerificationArgs,
    VerificationResponse
} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import authService from "@/lib/services/auth.service";
import cookieService from "@/lib/services/cookie-service";
import CookieService from "@/lib/services/cookie-service";

export async function authOTP(phone: string): Promise<ActionResult<AuthResponse>> {
    try {
        const responseResult = await authService.auth<AuthResponse>({payload: {phone}})
        return {
            success: true,
            data: responseResult.data,
            message: responseResult.data?.message
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function authVerification(args: VerificationArgs): Promise<ActionResult<VerificationResponse>> {
    try {
        const responseResult = await authService.verification<VerificationResponse>({payload: args})

        const token = `${responseResult.data.token_type} ${responseResult.data.access_token}`
        const decodedToken = authService.decodeJwtToken(token)
        console.log("***************************", decodedToken);
        
        await CookieService.setAuthorizationToken(token, decodedToken.exp)
        return {
            success: true,
            data: responseResult.data,
            message: responseResult.data?.message
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getUser(): Promise<ActionResult<GetUserResponse>> {
    try {
        const responseResult = await authService.getUser<GetUserResponse>();
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function logOut(): Promise<ActionResult<GetUserResponse>> {
    try {
        await cookieService.deleteAuthorizationToken()
        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

