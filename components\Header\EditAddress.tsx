'use client';

import { UserAddress } from '@/lib/types/types';
import { useState } from 'react';

export default function EditAddressForm({
  address,
  onCancel,
  onSave,
}: {
  address: UserAddress | null;
  onCancel: () => void;
  onSave: (updated: UserAddress) => void;
}) {
  const [fullAddress, setFullAddress] = useState(address?.address || '');
  const [details, setDetails] = useState(address?.address || '');
  const [title, setTitle] = useState(address?.receiver_name || '');
  const [phone, setPhone] = useState(address?.receiver_phone || '');

  function handleSubmit() {
    if (!address) return;
    const updated = { ...address, address: fullAddress, details, title, phone };
    onSave(updated);
  }

  return (
    <div className="flex flex-col gap-4 p-2">
      <div>
        <label className="text-sm font-medium mb-1 block">نشانی</label>
        <input
          value={fullAddress}
          onChange={(e) => setFullAddress(e.target.value)}
          className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
          placeholder="آدرس کامل"
        />
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">جزئیات آدرس</label>
        <input
          value={details}
          onChange={(e) => setDetails(e.target.value)}
          className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
          placeholder="جزئیات آدرس"
        />
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">عنوان آدرس</label>
        <input
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
          placeholder="مثلا منزل یا محل کار"
        />
      </div>

      <div>
        <label className="text-sm font-medium mb-1 block">شماره تماس (اختیاری)</label>
        <input
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
          placeholder="مثلا 09121234567"
        />
      </div>

      <button
        onClick={handleSubmit}
        className="bg-primary text-white py-3 rounded-xl w-full font-bold mt-4"
      >
        تایید و ویرایش آدرس
      </button>

      <button
        onClick={onCancel}
        className="text-gray-500 underline text-center mt-2"
      >
        بازگشت به لیست آدرس‌ها
      </button>
    </div>
  );
}
