'use client'

import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CameraIcon from "@/components/common/svg/CameraIcon";
import CustomButton from "@/components/UI/CustomButton";
import SubtractIcon from "@/components/common/svg/SubtractIcon";
import ResultDetailsUpIcon from "@/components/common/svg/ResultDetailsUpIcon";
import ResultDetailsLeftIcon from "@/components/common/svg/ResultDetailsLeftIcon";
import { useState } from "react";
import { cn } from "@/lib/utils";
import SubtractBorderIcon from "@/components/common/svg/SubtractBorderIcon";
import { PaymentDetail } from "@/lib/types/action-types";
import { getViolationImageAction } from "@/actions/inquiry.action";
// import DialogModal from "@/components/common/DialogModal";
import toast from "react-hot-toast";

type Props = {
    isCollapse?: boolean;
    detail: PaymentDetail
}


export default function InquiryResultWithDetails({ detail, isCollapse = true }: Props) {

    // const [loading, setLoading] = useState(false);

    const [collapse, setCollapse] = useState(isCollapse)
    // const handleViolationImage = async () => {
    //     setLoading(true);
    //     try {
    //         const response = await getViolationImageAction(detail.unique_id);
    //         const imageUrl = response?.data?.Parameters?.VehicleImageUrl;

    //         if (response.status === 200 && imageUrl) {
    //             window.location.assign(imageUrl);
    //         } else {
    //             toast.error("تصویری برای نمایش یافت نشد.");
    //         }
    //     } catch (error) {
    //         toast.error("خطا در دریافت تصویر.");
    //     } finally {
    //         setLoading(false);
    //     }
    // };
    return (
        <>
            {/* {loading && (
                <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
                    <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
            )} */}

            <ChoiceWrapper
                backgroundColor='#F9FAFB'
                borderColor={collapse ? "#EEEEEE" : "#CCCACA"}
                className={collapse ? 'border-solid !border' : ''}
            >
                <div
                    onClick={() => setCollapse(!collapse)}
                    className='relative  w-full cursor-pointer'>
                    <div className={cn('w-full p-2 pt-5 pb-10 max-h-[360px] duration-200 transition-all relative', {
                        "max-h-[115px]": collapse
                    })}>
                        <div className='absolute bottom-[-7px] left-[-13px]'>
                            {collapse ? <SubtractBorderIcon width={100} height={100} /> :
                                <SubtractIcon width={100} height={100} />}
                        </div>
                        <div className='absolute bottom-[-13px] left-[22px] cursor-pointer'
                            onClick={() => setCollapse((prev) => !prev)}>
                            {
                                !collapse ? (<ResultDetailsUpIcon width={30} height={30} />) : (<ResultDetailsLeftIcon />)
                            }
                        </div>
                        <div className='w-full flex justify-between items-center gap-x-1'>
                            <span>
                                <p className='text-[#000000]'>{detail.amount} <span className='text-xs'>تومان</span></p>
                                <p className='text-xs mt-1'>{detail.type}</p>
                            </span>
                            <CameraIcon height={40} width={40} />
                        </div>
                        {collapse && (<div className='mt-2 flex items-center gap-1 text-[#9DA5B0] text-xs'>
                            <span>تاریخ وقوع:</span>
                            <span>{detail.date_time}</span>
                        </div>)}
                        <div className={cn('mt-5 flex flex-col items-center gap-5', {
                            "hidden": collapse,
                        })}>
                            <div className='w-full flex justify-between items-center text-[#596068] text-xs'>
                                <span>تاریخ وقوع:</span>
                                <span>{detail.date_time}</span>
                            </div>
                            {/*<div className='w-full flex justify-between items-center text-[#596068]'>*/}
                            {/*    <span className='text-xs'>روش ثبت:</span>*/}
                            {/*    <span className='text-xs'>{detail.}</span>*/}
                            {/*</div>*/}
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>محل وقوع:</span>
                                <span className='text-xs'>{detail.location}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شناسه قبض:</span>
                                <span className='text-xs'>{detail.bill_id}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شناسه پرداخت:</span>
                                <span className='text-xs'>{detail.payment_id}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شماره ریال جریمه:</span>
                                <span className='text-xs'>{detail.serial_number}</span>
                            </div>
                            <div className="w-full ">
                                <CustomButton
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        window.open(detail.payment_url, "_blank");
                                    }}
                                    bgColor='' className="py-4" {...detail.payment_url == "#" ? {disabled: true} : {}}>پرداخت</CustomButton>
                                {/* <DialogModal title={"دریافت تصویر خلافی خودرو"} onConfirm={handleViolationImage} description={"هزینه استعلام برای هر تصویر خلافی 2500 تومان میباشد. آیا میخواهید ادامه دهید"}>
                                    <CustomButton className="py-1 flex flex-col text-xs w-[11rem]">
                                        دریافت تصویر خلافی
                                    </CustomButton>
                                </DialogModal> */}
                            </div>
                        </div>

                    </div>

                </div>
            </ChoiceWrapper>

        </>
    );
}
