
// import CartIcon from "@/components/common/svg/CartIcon"
import { ProductData, SimilarProduct } from "@/lib/types/product.types"
import ProductImage from "@/public/assets/images/product1.png"
import Subtract from "@/public/assets/images/subtract-border-gray.svg"
import { ShoppingBasket, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"


const Product = ({width, product}: {width?: string, product: SimilarProduct}) => {
  const {title, slug, rate, image, price } = product
  return (
    <Link href={`/product/${slug}`}>
      <div className={`bg-white mb-8 ${width || "product-w-32"} max-md:w-full rounded-3xl p-3 md:max-h-[340px] relative max-md:flex max-md:max-h-40 max-md:gap-3 max-md:items-center`}>
        <div className='bg-[#F9FAFB] relative flex justify-center md:mb-5 rounded-3xl max-md:max-w-32 h-auto'>
          <Image alt='' src={ProductImage} />
          <span className="max-md:hidden bottom-1 left-3 text-sm max-md:text-xs absolute flex items-start gap-1 bg-[#000] text-white w-fit md:py-1.5 md:px-2 rounded-3xl justify-center"> {rate?.toFixed(1)} <Star className="md:w-5 md:h-5 text-[#F7BC06]" fill="#F7BC06" /> </span>
        </div>
        <div className='card-details flex flex-col md:gap-5 max-md:gap-2'>
          <h3 className='mb-2 py-3'> {title} </h3>
          <span className='max-md:text-sm'> <strong>8,029,000</strong> تومان </span>
          <span className=" text-sm max-md:text-xs flex items-center gap-1 bg-[#F9FAFB]  w-fit py-1.5 px-2 rounded-3xl justify-center md:hidden"> 4/9 <Star className="w-5 h-4 text-[#F7BC06]" fill="#F7BC06" /> </span>
          <div className="absolute -bottom-[10px] -left-[10px]">
            <Image src={Subtract} alt="" />
          </div>
          <div>
            <button className='bg-gray-200 p-3 rounded-full md:z-20 absolute -bottom-0 md:left-8 max-md:left-8 transition-colors duration-300 hover:bg-yellow hover:text-white'>
              <ShoppingBasket className='md:z-20 w-6' />
            </button>

          </div>
        </div>

      </div>
    </Link>
  )
}

export default Product