'use client'

import { useState, useEffect } from 'react'
import { ChevronDown } from 'lucide-react'
import { Attribute, AttributeValue, Variation } from '@/lib/types/product.types'
import { cn } from '@/lib/utils'

interface SelectSizeProps {
    attributes: Attribute[]
    variations: Variation[]
    selectedColor?: AttributeValue | null
    defaultValue?: string | null
    onChange?: (size: string | null) => void
    disabled?: boolean
    selectedVariant: Variation | null
}

const SelectSize = ({
    variations,
    selectedColor,
    defaultValue = null,
    onChange,
    selectedVariant,
    disabled = false
}: SelectSizeProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedSize, setSelectedSize] = useState<string | null>(defaultValue)
    const [availableSizes, setAvailableSizes] = useState<string[]>([])
    console.log("______",selectedSize);
    
    // console.log(attributes);
    
    // Update available sizes when selected color changes
    useEffect(() => {
        if (selectedColor && variations.length > 0) {
            // Filter variations by the selected color
            const filteredVariations = variations.filter(
                variant => variant.color.toLowerCase() === selectedColor.value.toLowerCase()
            )
            
            // Extract unique sizes from filtered variations
            const sizes = [...new Set(filteredVariations.map(variant => variant.size))]
            setAvailableSizes(sizes)
            
            // If current selected size is not available, reset it
            if (selectedSize && !sizes.includes(selectedSize)) {
                setSelectedSize(null)
                if (onChange) onChange(null)
            }
            
            // If there's only one size available, auto-select it
            if (sizes.length === 1 && (!selectedSize || selectedSize !== sizes[0])) {
                setSelectedSize(sizes[0])
                if (onChange) onChange(sizes[0])
            }
        } else {
            setAvailableSizes([])
            setSelectedSize(null)
            if (onChange) onChange(null)
        }
    console.log(selectedSize);
    
    if (selectedVariant?.size) {
        handleSizeSelect(selectedVariant.size)
    }
    
    }, [selectedColor,selectedVariant, variations, onChange])

    
    const handleSizeSelect = (size: string) => {
        setSelectedSize(size)
        setIsOpen(false)
        if (onChange) {
            onChange(size)
        }
    }

    // Find the size attribute to get proper titles
    // const sizeAttribute = attributes.find(attr => attr.type === 'size')
    
    // Get the display title for a size value
    // const getSizeTitle = (size: string) => {
    //     if (!sizeAttribute) return size
    //     const sizeValue = sizeAttribute.values.find(v => v.value === size)
    //     return sizeValue?.title || size
    // }

    return (
        <div className="relative md:w-52 mb-5">
            <button
                type="button"
                className={cn(
                    "flex items-center justify-between w-full px-4 py-2 text-sm border rounded-lg",
                    isOpen ? "border-gray-500" : "border-gray-300",
                    disabled ? "bg-gray-100 text-gray-500 cursor-not-allowed" : "bg-white hover:border-gray-400",
                    !selectedColor && "opacity-70"
                )}
                onClick={() => !disabled && setIsOpen(!isOpen)}
                disabled={disabled || !selectedColor || availableSizes.length === 0}
            >
                <span>
                    {selectedSize ? selectedSize : 'انتخاب سایز'}
                </span>
                <ChevronDown className={cn(
                    "w-4 h-4 transition-transform",
                    isOpen && "transform rotate-180"
                )} />
            </button>

            {isOpen && availableSizes.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                   
                    <ul className="py-1">
                        {availableSizes.map(size => (
                            
                            <li
                                key={size}
                                className={cn(
                                    "px-4 py-2 text-sm cursor-pointer hover:bg-gray-100",
                                    selectedSize === size && "bg-gray-100 font-medium"
                                )}
                                onClick={() => handleSizeSelect(size)}
                            >
                                {/* {getSizeTitle(size)} */}
                                {size}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
            
            {/* {selectedColor && availableSizes.length === 0 && (
                <p className="mt-1 text-xs text-red-500">
                    هیچ سایزی برای این رنگ موجود نیست
                </p>
            )} */}
        </div>
    )
}

export default SelectSize
