'use client';
import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const Pagination = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 5;

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="flex items-center justify-center gap-2 mt-5 ">
      {/* Prev */}
      <button
        onClick={() => currentPage > 1 && goToPage(currentPage - 1)}
        className="w-9 h-9 rounded-full bg-blue-500 text-white flex items-center justify-center shadow hover:bg-blue-600 transition"
      >
        <ChevronRight size={16} />
      </button>

      {/* Ellipsis */}
      {currentPage < totalPages - 2 && (
        <span className="text-gray-500 text-sm px-1">...</span>
      )}

      {/* Page numbers (reversed) */}
      {[...Array(totalPages)].map((_, i) => {
        const page = totalPages - i;
        return (
          <button
            key={page}
            onClick={() => goToPage(page)}
            className={`w-9 h-9 rounded-full border text-sm flex items-center justify-center transition ${
              currentPage === page
                ? 'border-blue-500 text-blue-500'
                : 'border-gray-200 text-gray-700 hover:border-blue-300 hover:text-blue-400'
            }`}
          >
            {page}
          </button>
        );
      })}

      {/* Next */}
      <button
        onClick={() => currentPage < totalPages && goToPage(currentPage + 1)}
        className="w-9 h-9 rounded-full border border-gray-300 text-gray-600 flex items-center justify-center hover:bg-gray-100 transition"
      >
        <ChevronLeft size={16} />
      </button>
    </div>
  );
};

export default Pagination;
