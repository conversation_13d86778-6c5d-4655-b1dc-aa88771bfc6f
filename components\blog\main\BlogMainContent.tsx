import RoundedArrow from "@/components/common/svg/RoundedArrow"
import { InfoIcon } from "lucide-react"
import BlogSliderCard from "../BlogSliderCard"
import BlogSliderSection from "../BlogSliderSection"
import BlogHeadSection from "./BlogHeadSection"
import { Article } from "@/lib/types/types"
import { CateoryResponse } from "@/lib/types/article.types"
import Image from "next/image"
import Subtract from "@/public/assets/images/subtract-border-gray.svg"
import Pagination from "@/components/UI/Pagination"

// interface BlogMainContentProps {
//     ArticlesResponse: ArticlesApiResponse
//     CategoriesResponse: CateoryResponse
// }
type PageProps = {
  ArticlesResponse: Article[]
  CategoriesResponse: CateoryResponse
  articleTitle?: string
  articleDescription?: string
}
const BlogMainContent = async ({ ArticlesResponse, CategoriesResponse, articleDescription, articleTitle }: PageProps) => {
  //    const {  category } = await searchParams;
  // console.log(category);
  
  // const CategoriesResponse:CateoryResponse = await apiClient("categories")
  // .then(res => res.json())
  // const ArticlesResponse:ArticlesApiResponse = await apiClient(`articles${category ? `?category=${category}` : ''}`)
  // .then(res => res.json())

  console.log(articleDescription);
  return (
        <div className="mx-auto">
      <BlogHeadSection categories={CategoriesResponse?.data} />




      <section className="relative container mx-auto">
        <Image src={Subtract} alt="Subtract" className="absolute -top-9 right-[45%] max-md:right-[33%] w-40" />

        <div>
          <BlogSliderSection articles={ArticlesResponse} />
        </div>
        <div className="mb-10">
          <div className="flex items-center justify-center mb-10">
            <h2 className="text-xl">
              آخرین و جدیدترین مقالات سایت
            </h2>
            <RoundedArrow />
          </div>
          <div className="blog-posts flex flex-col md:grid md:grid-cols-3 md:gap-6 mx-auto px-4 md:px-8">
            {
              ArticlesResponse?.map((item, index: number) => (
                <BlogSliderCard {...item} key={index} postInfo={true} />
              ))
            }
            {/* <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} />
            <BlogSliderCard postInfo={true} /> */}
          </div>
          <div className='mt-auto w-full overflow-x-hidden pt-6 self-center'>
            <Pagination />
          </div>
        </div>
        {
          articleTitle && articleDescription &&
          <div className="bg-white mb-20 p-5 max-md:px-4 md:px-7 rounded-3xl max-md:w-[96%] max-md:mx-auto">
            <div className="flex items-center gap-5 md:text-lg ">
              <InfoIcon />
              <h2>
                {articleTitle}
              </h2>
            </div>
            <div className="text-justify leading-8 md:px-8 max-md:px-1 md:pr-12 mt-5 relative blog-pluses max-md:text-sm max-md:leading-7">
              <article dangerouslySetInnerHTML={{ __html: articleDescription }} />
            </div>
          </div>
        }
      </section>
    </div>
  )
}

export default BlogMainContent