import Image from "next/image"
import PaymentSuccess from "@/public/assets/images/payment-success.png"
import AddressWrapper from "@/components/shop/paymentResult/AddressWrapper"
import OrderSummary from "@/components/shop/checkout/OrderSummary"
import OrderSummaryList from "@/components/shop/checkout/OrderSummaryList"
import CustomButton from "@/components/UI/CustomButton"
const page = () => {
    return (
        <section className="min-h-screen max-md:overflow-hidden max-md:px-2 mb-10">
            <div className="max-w-5xl max-md:max-w-sm mx-auto max-md:pb-3 pb-5 mt-20 max-md:mt-8 p-3 max-md:p-1.5 bg-white rounded-3xl border border-gray-100 half-circle-payment-card">
                <div className="bg-gradient-to-b from-[#D8ECC9] to-transparent flex flex-col justify-center items-center rounded-3xl">
                    <div className="my-8">
                        <div className="flex justify-center">
                            <Image src={PaymentSuccess} alt="payment-success" />
                        </div>
                        <div>
                            <h2 className="text-xl max-md:text-lg">
                                سفارش شما با موفقیت ثبت شد
                            </h2>
                        </div>
                    </div>

                    <div className="flex justify-around w-full max-md:flex-wrap max-md:text-xs max-md:gap-y-3">
                        <div className="w-[18%] max-md:w-[30%]">
                            <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                                <span>
                                    شماره سفارش
                                </span>
                                <span className="text-yellow">
                                    #486346843
                                </span>
                            </p>
                        </div>
                        <div className="w-[18%] max-md:w-[30%]">
                            <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                                <span>
                                    تعداد کالا
                                </span>
                                <span className="text-primary">
                                    7 مورد
                                </span>
                            </p>
                        </div>
                       
                        <div className="w-[18%] max-md:w-[30%]">
                            <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white">
                                <span>
                                    تاریخ ثبت
                                </span>
                                <span className="">
                                    30 آذر 1403
                                </span>
                            </p>
                        </div>
                        <div className="w-[18%] max-md:w-full max-md:px-1">
                            <p className="flex flex-col gap-3 border-[3px] border-dashed rounded-xl px-3.5 py-4 bg-white max-md:justify-center max-md:items-center">
                                <span>
                                    مبلغ پرداختی
                                </span>
                                <span className="text-red-500 max-md:text-base">
                                    3000000 تومان
                                </span>
                            </p>
                        </div>
                       
                    </div>
                </div>
                <div className="my-5 md:w-[95%] mx-auto md:px-3 max-md:px-1">
                    <AddressWrapper />
                </div>
                
                <div className="md:w-[95%] mx-auto pt-10 mt-12 border-t-2 border-dashed max-md:px-1">
                    <h3 className="text-center text-xl">
                        خلاصه سفارش
                    </h3>
                    <OrderSummaryList className="md:w-full max-md:text-xs" />

                </div>
                <div className="flex justify-between md:w-[95%] mx-auto mt-6 md:px-3">
                    <CustomButton className="w-[48%] py-5 max-md:py-4 md:text-lg max-md:text-xs">
                        مشاهده فاکتور
                    </CustomButton>
                    <CustomButton className="w-[48%] py-5 max-md:py-4 md:text-lg max-md:text-xs bg-[#9DA5B0]">
                        خرید مجدد کالاها
                    </CustomButton>
                </div>
            </div>
        </section>
    )
}

export default page