import APIError from "@/lib/types/classes/api-error";
import {ActionResult} from "@/lib/types/action-types";
import {StatusCodes} from "http-status-codes";
import {redirect} from "next/navigation";
import {LOGIN_PATH} from "@/lib/routes";
import {logOut} from "@/actions/auth.action";


export async function handleActionError(error: ActionResult) {
    if (error.status === StatusCodes.UNAUTHORIZED) {
        await logOut();
        throw redirect(LOGIN_PATH)
    }
}

export function handleActionErrorResponse(error: APIError): ActionResult<any> {
    return {
        success: false,
        message: error.message,
        status: error.status
    }
}

export const decodeSearchParams = (searchParams: Record<string, string | string[]>): Record<string, string | string[]> => {
    return Object.fromEntries(
        Object.entries(searchParams).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.map(decodeURIComponent) : decodeURIComponent(value),
        ])
    );
}

export function formatWithComma(value: string) {
    if (!value || isNaN(Number(value))) return value
    return Number(value.replace(/,/g, "")).toLocaleString();
}

