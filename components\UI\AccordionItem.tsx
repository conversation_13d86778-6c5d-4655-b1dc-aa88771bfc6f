"use client";
import React, { useState, useRef, useEffect, ReactNode } from "react";

interface AccordionItemProps {
  title: string;
  index: number;
  isOpen: boolean;
  onClick: (index: number) => void;
  children: ReactNode;
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, index, isOpen, onClick, children }) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (contentRef.current) {
      setHeight(isOpen ? contentRef.current.scrollHeight : 0);
    }
  }, [isOpen]);

  const plusSVG = (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" className="w-4 h-4">
      <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
    </svg>
  );

  const minusSVG = (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" className="w-4 h-4">
      <path d="M3.75 7.25a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5Z" />
    </svg>
  );

  return (
    <div className={`my-2 leading-8 border-b ${isOpen ? "bg-primary text-white" : "bg-white text-gray-700"} text-right rounded-xl md:px-4 max-md:px-3 py-3 border-slate-200`}>
      <button onClick={() => onClick(index)} className="w-full flex justify-between items-center py-3">
        <span className="max-md:text-sm max-md:text-right max-md:w-[300px]" style={{lineHeight:"1.8rem"}}>{title}</span>
        <span className={`bg-gray-300 p-1 text-white rounded-full transition-transform duration-300 ${isOpen ? "rotate-180" : ""}`}>
          {isOpen ? minusSVG : plusSVG}
        </span>
      </button>
      <div
        ref={contentRef}
        style={{ maxHeight: `${height}px` }}
        className="overflow-hidden transition-all duration-500 ease-in-out"
      >
        <div className="pb-5 text-sm">{children}</div>
      </div>
    </div>
  );
};

export default AccordionItem