'use client'

import Card from "@/components/common/Card";
import useInquiryPost from "@/lib/hooks/useInquiryPost";
import {useAuth} from "@/lib/hooks/useAuth";
import usePathUrlHelper from "@/lib/hooks/usePathUrlHelper";
import {useRouter, useSearchParams} from "next/navigation";
import {useEffect, useState} from "react";
import {ViolationQueryParams} from "@/lib/types/types";
import {TransactionResponse} from "@/lib/types/action-types";
import toast from "react-hot-toast";
import {paymentVerificationPost} from "@/actions/payment.action";
import PaymentResultCard from "@/components/Wallet/result/PaymentResultCard";
import {formatWithComma} from "@/utils/helpers";
import CountDownTimer from "@/components/common/CountDownTimer";
import DisplayPlaque from "@/components/common/DisplayPlaque";
import {CAR_TICKETS_PATH, DASHBOARD_PATH, MOTOR_TICKETS_PATH} from "@/lib/routes";

type PaymentStateType = {
    success: boolean,
    actionButtonText: string,
    actionButtonLink: string,
    buttonHidden?: boolean,
    violationParams?: ViolationQueryParams,
    description?: string,
    message: string,
    data?: TransactionResponse
}

type InquiryState = {
    success: boolean,
    actionButtonText: string,
    actionButtonLink: string,
    message: string,
    description?: string,
}

export default function PaymentResult() {
    const {mutate} = useInquiryPost()
    const {reFetchUser} = useAuth()
    const {toPaymentUrl} = usePathUrlHelper();
    const searchParams = useSearchParams()
    const Authority = searchParams.get("Authority")
    const Status = searchParams.get("Status")
    const [isLoadingVerification, setIsLoadingVerification] = useState(true)
    const [inquiryState, setInquiryState] = useState<InquiryState>()
    const [paymentState, setPaymentState] = useState<PaymentStateType>()
    const [isLoadingInquiry, setIsLoadingInquiry] = useState(false)
    const router = useRouter()

    async function handleVerification(authority: string, status: string) {
        setIsLoadingVerification(true)


        const actionResult = await paymentVerificationPost({Authority: authority, Status: status})

        const result = actionResult.data;
        const hasInquiry = !!result?.plaque
        let violationParams: ViolationQueryParams | undefined;
        let paymentActionButtonText: string | undefined;
        let paymentMessageText: string | undefined;
        let paymentDescription: string | undefined;
        let paymentActionButtonLink = ''
        if (hasInquiry) {
            violationParams = {
                withDetails: result.details ? 'true' : 'false',
                phoneNumber: result.details?.phone,
                nationalCode: result.details?.national_id,
                inquiry: 'true',
                reInquiry: 'true',
                middle: result.plaque?.mid,
                left: result?.plaque?.left! || "",
                right: result?.plaque?.right! || "",
                alphabet: result?.plaque?.alphabet,
                isMotor: result.type === 'motor' ? 'true' : 'false',
            }
        }
        if (!actionResult.success) {
            paymentActionButtonLink = !hasInquiry ? toPaymentUrl({}, {currentUrlQuery: false}) : toPaymentUrl(violationParams, {currentUrlQuery: false})
            paymentMessageText = "ناموفق"
            paymentDescription = actionResult.message
            paymentActionButtonText = "پرداخت مجدد"
            toast.error(actionResult?.message || "")
        } else {
            await reFetchUser()
            paymentMessageText = "موفق"
            paymentActionButtonText = "مشاهده تاریخچه تراکنش ها"
            paymentActionButtonLink = DASHBOARD_PATH

        }
        setPaymentState({
            success: actionResult.success,
            message: paymentMessageText,
            actionButtonText: paymentActionButtonText!,
            actionButtonLink: paymentActionButtonLink,
            buttonHidden: hasInquiry,
            description: paymentDescription,
            violationParams: hasInquiry ? violationParams : undefined,
            data: actionResult.data
        })
        setIsLoadingVerification(false)

        if (hasInquiry && violationParams) {
            setIsLoadingInquiry(true)
            const mutateResult = await mutate(violationParams)

            let inquiryMessage = ""
            let actionButtonLink = ""
            let actionButtonText = ""
            let description = ""
            if (mutateResult.success) {
                inquiryMessage = 'موفق'
                actionButtonLink = mutateResult.href!
                actionButtonText = "مشاهده استعلام"
                description = "استعلام با موفقیت انجام شد."
            } else {
                inquiryMessage = 'ناموفق'
                actionButtonLink = violationParams.isMotor === 'true' ? MOTOR_TICKETS_PATH : CAR_TICKETS_PATH;
                actionButtonText = "استعلام مجدد"
                description = "در حین عملیات استعلام مشکلی پیش آمد. لطفا مجددا تلاش کنید."
            }
            setInquiryState({
                success: mutateResult.success,
                message: inquiryMessage,
                actionButtonLink,
                actionButtonText,
                description
            })
            setIsLoadingInquiry(false)

        }
    }

    useEffect(() => {
        if (Authority) {
            handleVerification(Authority, Status!)
        }
    }, [])

    function countDownRenderer(second: number) {
        return (
            <div className='w-8 h-8 border flex justify-center items-center mt-3 border-green-500 rounded-full'>
                <span className='text-sm text-neutral-600'>{String(second).padStart(2, "0")}</span>
            </div>
        )
    }

    return (
        <Card className='relative !py-4 !px-4'>
            <div className='w-full flex flex-col justify-center items-center'>
                {
                    (paymentState || isLoadingVerification) && (<PaymentResultCard
                        loading={isLoadingVerification}
                        status={paymentState?.success ? 'Success' : 'Failed'}
                        message={paymentState?.message || ""}
                        title="وضعیت پرداخت"
                        actionButtonText={paymentState?.actionButtonText}
                        actionButtonLink={paymentState?.actionButtonLink}
                        description={paymentState?.description}
                        buttonEnabled={!paymentState?.buttonHidden}
                    >
                        {paymentState?.success && (
                            <div className='p-4 flex flex-col gap-2'>
                                <div className='flex justify-between items-center'>
                                    <span className='text-gray-600 text-sm'>مبلغ تراکنش:</span>
                                    <div className='flex gap-1 text-gray-700 text-sm'>
                                        <span
                                        >{formatWithComma(paymentState.data?.amount.toString() || '')}
                                        </span>
                                        <span>تومان</span>
                                    </div>

                                </div>
                                <div className='flex justify-between items-center'>
                                    <span className='text-gray-600 text-sm'>شماره پیگیری:</span>
                                    <span
                                        className='text-gray-700 text-sm f'>{paymentState.data?.refId || ""}</span>
                                </div>
                                <div className='flex justify-between items-center'>
                                    <span className='text-gray-600 text-sm'>تاریخ تراکنش:</span>
                                    <span
                                        className='text-gray-700 text-sm '>{paymentState.data?.createdAt || ""}</span>
                                </div>
                            </div>
                        )}
                    </PaymentResultCard>)
                }
                <div className='h-[1px] w-3/4 bg-neutral-200 my-5'></div>
                {
                    (inquiryState || isLoadingInquiry) && (
                        <PaymentResultCard
                            loading={isLoadingInquiry}
                            status={inquiryState?.success ? 'Success' : 'Failed'}
                            hideMessage={inquiryState?.success}
                            message={inquiryState?.message || ""}
                            description={inquiryState?.description}
                            hideHeader={inquiryState?.success}
                            title="استعلام خلافی"
                            actionButtonText={inquiryState?.actionButtonText}
                            actionButtonLink={inquiryState?.actionButtonLink}
                        >
                            <div className='flex py-2 px-10 flex-col justify-center items-center'>
                                {inquiryState && inquiryState.success && paymentState?.violationParams &&
                                    <DisplayPlaque
                                        left={paymentState.violationParams.left}
                                        right={paymentState.violationParams.right}
                                        middle={paymentState.violationParams.middle}
                                        alphabet={paymentState.violationParams.alphabet}
                                        isMotor={paymentState.violationParams.isMotor === 'true'}/>
                                }
                                {
                                    inquiryState?.success && (
                                        <div className='my-4 flex flex-col justify-center items-center '>
                                            <p className='text-gray-600 text-sm '>انتقال به صفحه ی مشاهده استعلام پس از:</p>
                                            <CountDownTimer
                                                renderer={countDownRenderer}
                                                remainingTime={10}
                                                onComplete={() => {
                                                    router.replace(inquiryState?.actionButtonLink)
                                                }}
                                            />
                                        </div>
                                    )
                                }
                            </div>
                        </PaymentResultCard>
                    )
                }
            </div>

        </Card>
    );
}
