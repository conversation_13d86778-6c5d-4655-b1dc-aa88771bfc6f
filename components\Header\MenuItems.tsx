"use client"
import {ReactNode} from "react";
import {
    CAR_TICKETS_PATH,
    MOTOR_TICKETS_PATH,
    HOME_PATH,
    CONTACT_US_PATH,
    CAR_VIOLATION_IMAGE_PATH,
    CAR_INSURANCE_PATH,
    DRIVING_LICENSE_STATUS_PATH,
    DRIVING_LICENSE_POINT_PATH,
    CAR_ID_DOCUMENTS_PATH,
    PLATE_HISTORY_PATH,
    BLOG_PATH,
    RULES_PATH,
    PRIVACY_POLICY_PATH
} from "@/lib/routes";
import {Minus} from "lucide-react";
import {ServiceStatusType} from "@/lib/types/types";
// import envConfig from "@/lib/config-env";
import { getServicesStatus } from "@/lib/utils";
import { useServiceStatus } from "@/lib/providers/ServicesProvider";

type NavigationLink = {
    title: string;
    href: string;
    icon?: ReactNode;
    type: "mobile" | "desktop" | "both";
    status: ServiceStatusType
};

// const env = envConfig()
//  const { data } = useServiceStatus()
    
    // const {services: services_status } = data?.data 
    // const services_status = data?.data || {}
    
export const menuItems: NavigationLink[] = [
    {
        title: "صفحه اصلی",
        href: HOME_PATH, type: "both",
        icon: <Minus size={15}/>,
        status: "ACTIVE"
    },
    {
        title: "استعلام خلافی خودرو",
        href: CAR_TICKETS_PATH,
        type: "both", icon: <Minus size={15}/>,
        status:"ACTIVE"
    },
    {
        title: "استعلام خلافی موتور",
        href: MOTOR_TICKETS_PATH,
        type: "both", icon: <Minus size={15}/>,
        status: "ACTIVE"
    },
    {
        title: "تصویر تخلفات رانندگی",
        href: CAR_VIOLATION_IMAGE_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    {
        title: "استعلام بیمه شخص ثالث",
        href: CAR_INSURANCE_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    {
        title: "وضعیت گواهینامه",
        href: DRIVING_LICENSE_STATUS_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    {
        title: "وضعیت کارت و سند خودرو",
        href: CAR_ID_DOCUMENTS_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    {
        title: "استعلام نمره منفی",
        href: DRIVING_LICENSE_POINT_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    {
        title: "استعلام تاریخچه پلاک",
        href: PLATE_HISTORY_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "DEACTIVE"
    },
    // {title: "استعلام مالیات خودرو", href: CAR_TAX_PATH, type: "both", icon: <Minus size={15}/>, status: env.Services.},
    {
        title: "تماس با ما",
        href: CONTACT_US_PATH,
        type: "both",
        icon: <Minus size={15}/>,
        status: "ACTIVE"
    },
    {
        title: "وبلاگ",
        href: BLOG_PATH,
        type: "both",
        icon: <Minus size={15}/>,
        status: "ACTIVE"
    },
    {
        title: "قوانین و مقررات",
        href: RULES_PATH, type: "mobile",
        icon: <Minus size={15}/>,
        status: "ACTIVE"
    },
    {
        title: "سیاست حریم خصوصی",
        href: PRIVACY_POLICY_PATH,
        type: "mobile",
        icon: <Minus size={15}/>,
        status: "ACTIVE"
    },

];