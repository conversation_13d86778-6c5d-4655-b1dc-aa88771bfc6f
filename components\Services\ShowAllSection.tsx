import React, {ReactNode} from 'react'
import Dots from "@/public/assets/images/dots.webp"
import Image from 'next/image'
import Arrow from "@/public/assets/images/arrow.webp"

interface Props {
    children?: ReactNode
}

const ShowAllSection = ({children}: Props) => {
    return (
        <div
            className="w-full flex flex-row lg:flex-col lg:justify-center items-end lg:items-start justify-between gap-2">
            <div className="hidden lg:block">
                <Image src={Dots} alt="dots" className=' opacity-30'/>
            </div>
            <div className="flex items-center gap-x-3">
                <div className='block lg:hidden'>
                    <Image src={Dots} alt="dots" className=' opacity-30'/>
                </div>
                <div>
                    <div className="whitespace-nowrap max-w-[300px] font-black flex gap-1 items-center">
                        <h2 className='font-light text-[#242021] text-xl'>سرویس های خودراکس</h2>
                        <Image src={Arrow}
                               alt="arrow"/>
                    </div>
                    <p className="mt-1 text-sm md:text-base text-[#242021] font-light">
                        سرویس و خدمات خودراکس برای خودرو و موتور سیکلت
                        را اینجا میتوانید ببینید.
                    </p>
                </div>
            </div>
            {children}

        </div>
    )
}

export default ShowAllSection