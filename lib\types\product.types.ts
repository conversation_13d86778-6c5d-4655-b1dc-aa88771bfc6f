export interface ProductResponse {
  success: boolean;
  message: string;
  data: ProductData;
  status: number;
}

export interface ProductData {
  title: string;
  slug: string;
  description: string;
  comments_count: number;
  questions_count: number;
  product_rates_count: number;
  product_rate: number | null;
  meta: Meta;
  galleries: Gallery[];
  details: Detail[];
  variations: Variation[];
  default_variant: Variation;
  attributes: Attribute[];
  sizes: string[];
  colors: string[];
  guarantees: Guarantee[];
  delivery_methods: any[]; // Assuming empty array or define properly if you get data later
  shop: Shop;
  categories: string[];
}

export interface Meta {
  title: string;
  description: string;
  keywords: string;
}

export interface Gallery {
  url: string;
  caption: string;
}

export interface Detail {
  key: string;
  value: string;
}

export interface Variation {
  id: string;
  sku: string;
  price: number;
  sale_price: number
  current_quantity: number;
  size: string;
  color: string;
}

export interface Attribute {
  type: string;
  title: string;
  values: AttributeValue[];
}

export interface AttributeValue {
  value: string;
  title: string;
  extra_data?: ExtraData;
}

export interface ExtraData {
  hex: string;
}

export interface Guarantee {
  id: string;
  price: number;
  months: number;
  company_name: string;
}

export interface Shop {
  id: string;
  title: string;
}

interface ProductHelpData {
  id: string;
  title: string;
  order: number;
  content: string; // contains HTML string
}

export interface ProductHelpResponse {
  success: boolean;
  message: string;
  data: ProductHelpData[];
  status: number;
}

export interface ProductCommentsResponse {
  success: boolean;
  message: string;
  status: number;
  data: ProductComment[];
}
export interface ProductComment {
  id: string;
  body: string;
  rate: number;
  has_bought: boolean;
  created_at: string; // ISO date string
  user: CommentUser;
  replies?: CommentReply[];
}


export interface CommentUser {
  id: string;
  full_name: string;
}

export interface CommentReply {
  id: string;
  body: string;
  rate: number | null;
  has_bought: boolean | null;
  created_at: string; // ISO date string
}

export interface User {
  id: string;
  name: string;
}

export interface Answer {
  id: string;
  body: string;
  created_at: string;
  user: User;
}

export interface Question {
  id: string;
  body: string;
  created_at: string;
  user: User;
  answers: Answer[];
}

export interface ProductQuestionsResponse {
  success: boolean;
  message: string;
  data: Question[];
  status: number;
}

export interface SimilarProductsResponse {
  success: boolean;
  message: string;
  data: SimilarProduct[];
  status: number;
}

export interface SimilarProduct {
  title: string;
  slug: string;
  image: {
    url: string;
    caption: string;
  };
  rate: number;
  price: number;
}

export interface CartItemAttribute {
  type: string;
  title: string;
  value: string;
  extra_data: {
    [key: string]: any;
  } | null;
}

export interface CartItem {
  product_id: string;
  id: string;
  name: string;
  sku: string;
  price: number;
  quantity: number;
  total: number;
  image: string;
  attributes: CartItemAttribute[];
}
